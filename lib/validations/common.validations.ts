/**
 * Common validation schemas
 */

import { z } from 'zod';

export const coordinatesSchema = z.object({
  lat: z.number().min(-90).max(90),
  lng: z.number().min(-180).max(180),
});

export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
});

export const idSchema = z.string().min(1, 'ID is required');

export const emailSchema = z.string().email('Please enter a valid email address');

export const phoneSchema = z.string()
  .min(10, 'Phone number must be at least 10 digits')
  .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number');

export const urlSchema = z.string().url('Please enter a valid URL').optional().or(z.literal(''));

export const dateSchema = z.string().refine((date) => !isNaN(Date.parse(date)), {
  message: 'Please enter a valid date',
});

export const businessTypeSchema = z.enum(['INDIVIDUAL', 'COMPANY']);

export const languageSchema = z.enum(['en', 'fr']);
