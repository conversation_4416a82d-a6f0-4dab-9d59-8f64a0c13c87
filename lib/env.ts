/**
 * Environment configuration
 * This file centralizes all environment variables with proper typing and default values
 */

// Environment type
export type Environment = 'development' | 'production' | 'test'

// Helper function to get the current environment
const getEnvironment = (): Environment => {
  const env = process.env.NODE_ENV;
  if (env === 'production' || env === 'development' || env === 'test') {
    return env;
  }
  return 'development';
};

// Determine current environment
const currentEnv = getEnvironment();
const isDev = currentEnv === 'development';
const isProd = currentEnv === 'production';

// Get the development port from environment or use default
const devHost = process.env.NEXT_PUBLIC_DEV_HOST || 'localhost';

// Function to get the current port from window.location if available
const getCurrentPort = (): string => {
  if (typeof window !== 'undefined') {
    return window.location.port || '3000';
  }
  return process.env.NEXT_PUBLIC_DEV_PORT || '3000';
};

// Environment variables with defaults
export const env = {
  // Current environment
  environment: currentEnv,
  
  // Public environment (can be different from NODE_ENV)
  ENVIRONMENT: currentEnv,
  
  // API URL
  API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8081',
  
  // Base URL (frontend)
  BASE_URL: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
  
  // Development server config
  DEV: {
    HOST: devHost,
    // This will be updated client-side with the actual port
    getPort: getCurrentPort,
    getUrl: () => {
      const port = getCurrentPort();
      return `http://${devHost}:${port}`;
    }
  },
  
  // Auth config
  AUTH: {
    DOMAIN: process.env.NEXT_PUBLIC_COGNITO_DOMAIN || 'ca-central-1mzuuzispv.auth.ca-central-1.amazoncognito.com',
    CLIENT_ID: process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || '58km7m63f6tnlilr3kg4kq3ht8',
    CLIENT_SECRET: process.env.NEXT_PUBLIC_COGNITO_CLIENT_SECRET || 'o9c6hqheg9kive9ugu2je23j1rv02qb0fjn2ql2cscv3or7n3d1',
    USER_POOL_ID: process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID || 'ca-central-1_MzUUZiSpV',
    REGION: process.env.NEXT_PUBLIC_COGNITO_REGION || 'ca-central-1',
    // Use dynamic callback URL based on environment
    getRedirectUri: () => {
      if (typeof window === 'undefined') return '';
      
      // Always use /dashboard for all environments
      const port = getCurrentPort();
      const host = isDev ? `${devHost}:${port}` : window.location.host;
      
      // Force http for localhost, https for other hosts
      const protocol = host.includes('localhost') ? 'http' : 'https';
      
      console.log(`Building redirect URI with: protocol=${protocol}, host=${host}, isDev=${isDev}, env=${currentEnv}`);
      
      return `${protocol}://${host}/dashboard`;
    }
  },

  // Mapbox configuration
  MAPBOX: {
    TOKEN: process.env.NEXT_PUBLIC_MAPBOX_TOKEN || 'pk.eyJ1IjoiZWxtb2hhZGViIiwiYSI6ImNtNHQ2dnZwYzA5MHcya3B5YjJoN3B5aTUifQ.OJadTL-8skKILjTUCTtY7w'
  },

  // Environment flags
  isProd,
  isDev,

  // Helper functions
  getApiUrl: () => {
    const currentEnv = getEnvironment();
    if (currentEnv === 'production') {
      return 'https://api.vestral.com';
    }
    return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8081';
  }
}

/**
 * Get environment-specific API URL
 * @param path API path (without leading slash)
 * @returns Full API URL with path
 */
export function getApiUrl(path: string = ''): string {
  switch (getEnvironment()) {
    case 'production':
      return `https://api.vestral.com/${path}`;
    default:
      const baseUrl = env.API_URL.endsWith('/') 
        ? env.API_URL.slice(0, -1) 
        : env.API_URL
      const formattedPath = path.startsWith('/') ? path : `/${path}`
      return `${baseUrl}${formattedPath}`
  }
}

export default env
