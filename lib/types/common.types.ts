/**
 * Common types used across the application
 */

export interface ApiResponse<T = any> {
  data: T;
  error?: string;
  status: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
}

export interface ErrorResponse {
  error: string;
  status: number;
  details?: Record<string, any>;
}

export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

export type Language = 'en' | 'fr';
export type BusinessType = 'INDIVIDUAL' | 'COMPANY';
export type AccountType = 'individual' | 'company';

export interface Coordinates {
  lat: number;
  lng: number;
}
