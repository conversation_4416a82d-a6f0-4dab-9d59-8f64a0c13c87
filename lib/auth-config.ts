import env from './env';

// Client-side configuration
export const COGNITO_CONFIG = {
  DOMAIN: 'ca-central-1mzuuzispv.auth.ca-central-1.amazoncognito.com',
  CLIENT_ID: '58km7m63f6tnlilr3kg4kq3ht8',
  CLIENT_SECRET: env.AUTH.CLIENT_SECRET,
  USER_POOL_ID: env.AUTH.USER_POOL_ID,
  REGION: 'ca-central-1',
  getRedirectUri: () => {
    if (typeof window === 'undefined') return '';
    
    // Use the redirect URI that matches what's configured in Cognito
    const port = typeof window !== 'undefined' ? window.location.port : '3000';
    const host = env.isDev ? `${env.DEV.HOST}:${port}` : window.location.host;
    
    // Force http for localhost, https for other hosts
    const protocol = host.includes('localhost') ? 'http' : 'https';
    
    // Log the values for debugging
    console.log(`Auth config building redirect URI: protocol=${protocol}, host=${host}, isDev=${env.isDev}, env=${env.ENVIRONMENT}`);
    
    // This must match what's configured in your Cognito User Pool Client settings
    return `${protocol}://${host}/dashboard`;
  }
};

// Default export for easier imports
export default COGNITO_CONFIG;