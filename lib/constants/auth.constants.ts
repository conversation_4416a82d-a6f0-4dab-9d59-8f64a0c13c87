/**
 * Authentication-related constants
 */

export const COOKIE_NAMES = {
  SESSION: 'session',
  REFRESH_TOKEN: 'refresh_token',
  ID_TOKEN: 'id_token',
} as const;

export const PUBLIC_ROUTES = [
  '/',
  '/login',
  '/signup',
  '/logout',
  '/api/auth/callback',
  '/api/auth/logout',
] as const;

export const PROTECTED_ROUTE_PREFIXES = [
  '/dashboard',
] as const;

export const AUTH_ERRORS = {
  INVALID_CREDENTIALS: 'Invalid email or password',
  USER_NOT_FOUND: 'User not found',
  EMAIL_ALREADY_EXISTS: 'Email already exists',
  WEAK_PASSWORD: 'Password is too weak',
  INVALID_TOKEN: 'Invalid or expired token',
  SESSION_EXPIRED: 'Session has expired',
  UNAUTHORIZED: 'Unauthorized access',
} as const;

export const PASSWORD_REQUIREMENTS = {
  MIN_LENGTH: 8,
  REQUIRE_UPPERCASE: true,
  REQUIRE_LOWERCASE: true,
  REQUIRE_NUMBER: true,
  REQUIRE_SPECIAL_CHAR: false,
} as const;

export const SESSION_CONFIG = {
  MAX_AGE: 24 * 60 * 60, // 24 hours in seconds
  REFRESH_THRESHOLD: 5 * 60, // 5 minutes in seconds
} as const;
