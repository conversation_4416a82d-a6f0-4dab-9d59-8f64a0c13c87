/**
 * Market Data Service
 * 
 * Centralized service for all market data and statistics API calls
 */

import { api } from '../api-instance'
import type { ApiResponse, PaginatedResponse } from '@/lib/types'

// Market Data Types
export interface CityStatistics {
  id: string
  name: string
  numberOfBedrooms: number
  averagePrice: number
  totalProperties: number
  occupancyRate: number
  priceChange: number // percentage change from previous period
  marketTrend: 'UP' | 'DOWN' | 'STABLE'
}

export interface PostalCodeStatistics {
  id: string
  name: string // postal code
  numberOfBedrooms: number
  averagePrice: number
  totalProperties: number
  occupancyRate: number
  priceChange: number
  marketTrend: 'UP' | 'DOWN' | 'STABLE'
  cityName?: string
}

export interface MarketTrend {
  period: string
  averagePrice: number
  totalListings: number
  averageDaysOnMarket: number
  occupancyRate: number
}

export interface PropertyComparison {
  propertyId: string
  address: string
  price: number
  bedrooms: number
  bathrooms: number
  squareFootage?: number
  distance: number // distance from reference property
  pricePerSqFt?: number
}

export interface MarketAnalysis {
  location: string
  averageRent: number
  medianRent: number
  priceRange: {
    min: number
    max: number
  }
  competitorCount: number
  demandScore: number
  recommendedPrice: number
  confidence: number
}

export interface MarketFilters {
  city?: string
  postalCode?: string
  bedrooms?: number
  minPrice?: number
  maxPrice?: number
  propertyType?: string
}

// Market Data Service
const marketDataService = {
  /**
   * Get city statistics with pagination
   */
  async getCityStatistics(page: number = 1, limit: number = 10, filters?: MarketFilters): Promise<ApiResponse<PaginatedResponse<CityStatistics>>> {
    const params: Record<string, string> = {
      page: page.toString(),
      limit: limit.toString(),
    }

    if (filters?.bedrooms) params.bedrooms = filters.bedrooms.toString()
    if (filters?.minPrice) params.minPrice = filters.minPrice.toString()
    if (filters?.maxPrice) params.maxPrice = filters.maxPrice.toString()
    if (filters?.propertyType) params.propertyType = filters.propertyType

    return api.get<PaginatedResponse<CityStatistics>>('/statistics/cities', { params })
  },

  /**
   * Get postal code statistics with pagination
   */
  async getPostalCodeStatistics(page: number = 1, limit: number = 10, filters?: MarketFilters): Promise<ApiResponse<PaginatedResponse<PostalCodeStatistics>>> {
    const params: Record<string, string> = {
      page: page.toString(),
      limit: limit.toString(),
    }

    if (filters?.city) params.city = filters.city
    if (filters?.bedrooms) params.bedrooms = filters.bedrooms.toString()
    if (filters?.minPrice) params.minPrice = filters.minPrice.toString()
    if (filters?.maxPrice) params.maxPrice = filters.maxPrice.toString()
    if (filters?.propertyType) params.propertyType = filters.propertyType

    return api.get<PaginatedResponse<PostalCodeStatistics>>('/statistics/postal-codes', { params })
  },

  /**
   * Get market trends for a specific location
   */
  async getMarketTrends(location: string, period: 'MONTHLY' | 'QUARTERLY' | 'YEARLY' = 'MONTHLY'): Promise<ApiResponse<MarketTrend[]>> {
    return api.get<MarketTrend[]>('/market-data/trends', { 
      params: { location, period } 
    })
  },

  /**
   * Get comparable properties
   */
  async getComparableProperties(propertyId: string, radius: number = 5): Promise<ApiResponse<PropertyComparison[]>> {
    return api.get<PropertyComparison[]>('/market-data/comparables', {
      params: { propertyId, radius: radius.toString() }
    })
  },

  /**
   * Get market analysis for a property
   */
  async getMarketAnalysis(address: string, bedrooms: number, bathrooms: number, squareFootage?: number): Promise<ApiResponse<MarketAnalysis>> {
    const params: Record<string, string> = {
      address,
      bedrooms: bedrooms.toString(),
      bathrooms: bathrooms.toString(),
    }
    
    if (squareFootage) params.squareFootage = squareFootage.toString()

    return api.get<MarketAnalysis>('/market-data/analysis', { params })
  },

  /**
   * Get rent recommendations
   */
  async getRentRecommendation(propertyId: string): Promise<ApiResponse<{ recommendedRent: number, confidence: number, factors: string[] }>> {
    return api.get<{ recommendedRent: number, confidence: number, factors: string[] }>(`/market-data/rent-recommendation/${propertyId}`)
  },

  /**
   * Get neighborhood statistics
   */
  async getNeighborhoodStats(lat: number, lng: number, radius: number = 2): Promise<ApiResponse<any>> {
    return api.get<any>('/market-data/neighborhood', {
      params: { 
        lat: lat.toString(), 
        lng: lng.toString(), 
        radius: radius.toString() 
      }
    })
  },

  /**
   * Get market heat map data
   */
  async getMarketHeatMap(city: string, bedrooms?: number): Promise<ApiResponse<any[]>> {
    const params: Record<string, string> = { city }
    if (bedrooms) params.bedrooms = bedrooms.toString()

    return api.get<any[]>('/market-data/heatmap', { params })
  },

  /**
   * Get price history for a location
   */
  async getPriceHistory(location: string, bedrooms: number, months: number = 12): Promise<ApiResponse<any[]>> {
    return api.get<any[]>('/market-data/price-history', {
      params: { 
        location, 
        bedrooms: bedrooms.toString(), 
        months: months.toString() 
      }
    })
  },

  /**
   * Get demand indicators
   */
  async getDemandIndicators(location: string): Promise<ApiResponse<any>> {
    return api.get<any>('/market-data/demand', { params: { location } })
  },

  /**
   * Get investment metrics
   */
  async getInvestmentMetrics(propertyId: string): Promise<ApiResponse<any>> {
    return api.get<any>(`/market-data/investment-metrics/${propertyId}`)
  },

  /**
   * Search locations for market data
   */
  async searchLocations(query: string): Promise<ApiResponse<{ cities: string[], postalCodes: string[] }>> {
    return api.get<{ cities: string[], postalCodes: string[] }>('/market-data/search-locations', {
      params: { q: query }
    })
  },

  /**
   * Get market summary
   */
  async getMarketSummary(): Promise<ApiResponse<any>> {
    return api.get<any>('/market-data/summary')
  },

  /**
   * Get Quebec-specific statistics (for the Quebec statistics page)
   */
  async getQuebecStatistics(): Promise<ApiResponse<{ cities: CityStatistics[], postalCodes: PostalCodeStatistics[] }>> {
    return api.get<{ cities: CityStatistics[], postalCodes: PostalCodeStatistics[] }>('/statistics/quebec')
  }
}

export default marketDataService
