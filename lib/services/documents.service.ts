/**
 * Documents Service
 * 
 * Centralized service for all document management API calls
 */

import { api } from '../api-instance'
import type { ApiResponse, PaginatedResponse } from '@/lib/types'

// Document Types
export interface Document {
  id: string
  name: string
  type: 'LEASE' | 'RECEIPT' | 'INSPECTION' | 'MAINTENANCE' | 'INSURANCE' | 'TAX' | 'OTHER'
  category: 'PROPERTY' | 'TENANT' | 'LEASE' | 'PAYMENT' | 'MAINTENANCE' | 'LEGAL' | 'FINANCIAL'
  url: string
  size: number
  mimeType: string
  uploadedBy: string
  relatedEntityId?: string // Property ID, Tenant ID, Lease ID, etc.
  relatedEntityType?: 'PROPERTY' | 'TENANT' | 'LEASE' | 'PAYMENT' | 'MAINTENANCE'
  tags?: string[]
  isPublic: boolean
  expiryDate?: string
  createdAt: string
  updatedAt: string
}

export interface UploadDocumentRequest {
  file: File
  name?: string
  type: 'LEASE' | 'RECEIPT' | 'INSPECTION' | 'MAINTENANCE' | 'INSURANCE' | 'TAX' | 'OTHER'
  category: 'PROPERTY' | 'TENANT' | 'LEASE' | 'PAYMENT' | 'MAINTENANCE' | 'LEGAL' | 'FINANCIAL'
  relatedEntityId?: string
  relatedEntityType?: 'PROPERTY' | 'TENANT' | 'LEASE' | 'PAYMENT' | 'MAINTENANCE'
  tags?: string[]
  isPublic?: boolean
  expiryDate?: string
}

export interface DocumentFilters {
  type?: string
  category?: string
  relatedEntityId?: string
  relatedEntityType?: string
  tags?: string[]
  isPublic?: boolean
  search?: string
}

export interface DocumentFolder {
  id: string
  name: string
  parentId?: string
  documentCount: number
  createdAt: string
}

// Documents Service
const documentsService = {
  /**
   * Get all documents with pagination and filters
   */
  async getDocuments(page: number = 1, limit: number = 10, filters?: DocumentFilters): Promise<ApiResponse<PaginatedResponse<Document>>> {
    const params: Record<string, string> = {
      page: page.toString(),
      limit: limit.toString(),
    }

    if (filters?.type) params.type = filters.type
    if (filters?.category) params.category = filters.category
    if (filters?.relatedEntityId) params.relatedEntityId = filters.relatedEntityId
    if (filters?.relatedEntityType) params.relatedEntityType = filters.relatedEntityType
    if (filters?.isPublic !== undefined) params.isPublic = filters.isPublic.toString()
    if (filters?.search) params.search = filters.search
    if (filters?.tags) params.tags = filters.tags.join(',')

    return api.get<PaginatedResponse<Document>>('/documents', { params })
  },

  /**
   * Get a document by ID
   */
  async getDocumentById(id: string): Promise<ApiResponse<Document>> {
    return api.get<Document>(`/documents/${id}`)
  },

  /**
   * Upload a new document
   */
  async uploadDocument(data: UploadDocumentRequest): Promise<ApiResponse<Document>> {
    const formData = new FormData()
    formData.append('file', data.file)
    
    if (data.name) formData.append('name', data.name)
    formData.append('type', data.type)
    formData.append('category', data.category)
    if (data.relatedEntityId) formData.append('relatedEntityId', data.relatedEntityId)
    if (data.relatedEntityType) formData.append('relatedEntityType', data.relatedEntityType)
    if (data.tags) formData.append('tags', data.tags.join(','))
    if (data.isPublic !== undefined) formData.append('isPublic', data.isPublic.toString())
    if (data.expiryDate) formData.append('expiryDate', data.expiryDate)

    return api.post<Document>('/documents', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  /**
   * Update document metadata
   */
  async updateDocument(id: string, data: Partial<UploadDocumentRequest>): Promise<ApiResponse<Document>> {
    const updateData = { ...data }
    delete updateData.file // Remove file from update data
    return api.put<Document>(`/documents/${id}`, updateData)
  },

  /**
   * Delete a document
   */
  async deleteDocument(id: string): Promise<ApiResponse<void>> {
    return api.delete<void>(`/documents/${id}`)
  },

  /**
   * Download a document
   */
  async downloadDocument(id: string): Promise<ApiResponse<Blob>> {
    return api.get<Blob>(`/documents/${id}/download`)
  },

  /**
   * Get document preview URL
   */
  async getDocumentPreview(id: string): Promise<ApiResponse<{ previewUrl: string }>> {
    return api.get<{ previewUrl: string }>(`/documents/${id}/preview`)
  },

  /**
   * Search documents
   */
  async searchDocuments(query: string): Promise<ApiResponse<Document[]>> {
    return api.get<Document[]>('/documents/search', { params: { q: query } })
  },

  /**
   * Get documents by entity (property, tenant, lease, etc.)
   */
  async getDocumentsByEntity(entityType: string, entityId: string): Promise<ApiResponse<Document[]>> {
    return api.get<Document[]>(`/documents/entity/${entityType}/${entityId}`)
  },

  /**
   * Get expiring documents
   */
  async getExpiringDocuments(days: number = 30): Promise<ApiResponse<Document[]>> {
    return api.get<Document[]>('/documents/expiring', { params: { days: days.toString() } })
  },

  /**
   * Bulk upload documents
   */
  async bulkUploadDocuments(files: File[], category: string, relatedEntityId?: string): Promise<ApiResponse<Document[]>> {
    const formData = new FormData()
    files.forEach((file) => {
      formData.append('files', file)
    })
    formData.append('category', category)
    if (relatedEntityId) formData.append('relatedEntityId', relatedEntityId)

    return api.post<Document[]>('/documents/bulk', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  /**
   * Get document folders
   */
  async getFolders(): Promise<ApiResponse<DocumentFolder[]>> {
    return api.get<DocumentFolder[]>('/documents/folders')
  },

  /**
   * Create document folder
   */
  async createFolder(name: string, parentId?: string): Promise<ApiResponse<DocumentFolder>> {
    return api.post<DocumentFolder>('/documents/folders', { name, parentId })
  },

  /**
   * Move document to folder
   */
  async moveDocumentToFolder(documentId: string, folderId: string): Promise<ApiResponse<Document>> {
    return api.put<Document>(`/documents/${documentId}/move`, { folderId })
  }
}

export default documentsService
