// Public Properties Service - No authentication required

// Backend API response structure
export interface BackendProperty {
  id: string
  title: string
  description: string
  address: string
  price: number // BigDecimal from backend
  images: string[]
  bedroomNumber: number
  bathroomNumber: number
  attitude: number // latitude
  longitude: number
}

// Frontend display structure (converted from backend)
export interface PublicProperty {
  id: string
  title: string
  address: string
  price: number
  bedrooms: number
  bathrooms: number
  parking: number
  area?: number
  images: string[]
  type: 'Apartment' | 'House' | 'Studio' | 'Condo'
  status: 'LIVE'
  description: string
  amenities: string[]
  features: string[]
  landlord: {
    name: string
    phone: string
    email: string
  }
  availableDate: string
  coordinates?: {
    lat: number
    lng: number
  }
}

export interface PropertySearchFilters {
  query?: string
  priceMin?: number
  priceMax?: number
  bedrooms?: number
  bathrooms?: number
  propertyType?: string
  location?: string
  features?: string[]
  category?: string
  page?: number
  limit?: number
}

export interface PropertySearchResponse {
  properties: PublicProperty[]
  total: number
  page: number
  totalPages: number
}

class PublicPropertiesService {
  private baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'

  // Convert backend property to frontend format
  private convertBackendProperty(backendProperty: BackendProperty): PublicProperty {
    return {
      id: backendProperty.id,
      title: backendProperty.title,
      address: backendProperty.address,
      price: backendProperty.price,
      bedrooms: backendProperty.bedroomNumber,
      bathrooms: backendProperty.bathroomNumber,
      parking: 0, // Default value since not provided by backend
      area: undefined, // Not provided by backend
      images: backendProperty.images,
      type: 'Apartment', // Default type, could be enhanced with backend field
      status: 'LIVE',
      description: backendProperty.description,
      amenities: [], // Default empty, could be enhanced
      features: [], // Default empty, could be enhanced
      landlord: {
        name: 'Property Owner', // Default value
        phone: 'Contact for details',
        email: '<EMAIL>'
      },
      availableDate: new Date().toISOString().split('T')[0], // Default to today
      coordinates: {
        lat: backendProperty.attitude, // Note: backend uses 'attitude' for latitude
        lng: backendProperty.longitude
      }
    }
  }

  async searchProperties(filters: PropertySearchFilters = {}): Promise<PropertySearchResponse> {
    try {
      const queryParams = new URLSearchParams()

      if (filters.query) queryParams.append('query', filters.query)
      if (filters.priceMin) queryParams.append('priceMin', filters.priceMin.toString())
      if (filters.priceMax) queryParams.append('priceMax', filters.priceMax.toString())
      if (filters.bedrooms) queryParams.append('bedrooms', filters.bedrooms.toString())
      if (filters.bathrooms) queryParams.append('bathrooms', filters.bathrooms.toString())
      if (filters.propertyType) queryParams.append('propertyType', filters.propertyType)
      if (filters.location) queryParams.append('location', filters.location)
      if (filters.features) queryParams.append('features', filters.features.join(','))
      if (filters.category) queryParams.append('category', filters.category)
      if (filters.page) queryParams.append('page', filters.page.toString())
      if (filters.limit) queryParams.append('limit', filters.limit.toString())

      const queryString = queryParams.toString()
      const url = `${this.baseUrl}/public/properties${queryString ? `?${queryString}` : ''}`

      console.log('Fetching properties from:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // No Authorization header for public endpoint
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const backendProperties: BackendProperty[] = await response.json()

      // Convert backend properties to frontend format
      const properties = backendProperties.map(prop => this.convertBackendProperty(prop))

      // Apply frontend filters that might not be handled by backend
      let filteredProperties = properties

      if (filters.features && filters.features.length > 0) {
        // Frontend filtering for features since backend might not support this
        filteredProperties = filteredProperties.filter(p =>
          filters.features!.every(feature => p.features.includes(feature))
        )
      }

      if (filters.category && filters.category !== 'all' && filters.category === 'commercial') {
        // Filter out residential properties if commercial is selected
        filteredProperties = []
      }

      return {
        properties: filteredProperties,
        total: filteredProperties.length,
        page: filters.page || 1,
        totalPages: Math.ceil(filteredProperties.length / (filters.limit || 10))
      }
    } catch (error) {
      console.error('Error searching properties:', error)
      // Return mock data for development/fallback
      return this.getMockProperties(filters)
    }
  }

  async getPropertyById(id: string): Promise<PublicProperty | null> {
    try {
      const response = await fetch(`${this.baseUrl}/public/properties/${id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // No Authorization header for public endpoint
        },
      })

      if (!response.ok) {
        if (response.status === 404) {
          return null
        }
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const backendProperty: BackendProperty = await response.json()
      return this.convertBackendProperty(backendProperty)
    } catch (error) {
      console.error('Error fetching property:', error)
      // Return mock data for development/fallback
      return this.getMockPropertyById(id)
    }
  }

  async getFeaturedProperties(limit: number = 6): Promise<PublicProperty[]> {
    try {
      // For featured properties, we'll just get the first few properties
      const response = await this.searchProperties({ limit })
      return response.properties.slice(0, limit)
    } catch (error) {
      console.error('Error fetching featured properties:', error)
      // Return mock data for development/fallback
      return this.getMockFeaturedProperties(limit)
    }
  }

  // Mock data methods for development
  private getMockProperties(filters: PropertySearchFilters): PropertySearchResponse {
    const mockProperties: PublicProperty[] = [
      {
        id: "1",
        title: "Modern Downtown Apartment",
        address: "123 Rue Saint-Catherine, Montreal, QC H3B 1A7",
        price: 1800,
        bedrooms: 2,
        bathrooms: 1,
        parking: 1,
        area: 850,
        images: ["/api/placeholder/800/600", "/api/placeholder/800/600"],
        type: "Apartment",
        status: "LIVE",
        description: "Beautiful modern apartment in the heart of downtown Montreal with stunning city views.",
        amenities: ["Hardwood floors", "Stainless steel appliances", "In-unit laundry", "Air conditioning", "Balcony", "Gym access"],
        features: ["elevator", "pets", "furnished"],
        landlord: {
          name: "Property Management Inc.",
          phone: "(*************",
          email: "<EMAIL>"
        },
        availableDate: "2024-02-01",
        coordinates: { lat: 45.5017, lng: -73.5673 }
      },
      {
        id: "2",
        title: "Cozy Studio in Plateau",
        address: "456 Avenue Mont-Royal, Montreal, QC H2T 1V6",
        price: 1200,
        bedrooms: 1,
        bathrooms: 1,
        parking: 0,
        area: 450,
        images: ["/api/placeholder/800/600", "/api/placeholder/800/600"],
        type: "Studio",
        status: "LIVE",
        description: "Charming studio apartment in trendy Plateau neighborhood with exposed brick walls.",
        amenities: ["Exposed brick", "High ceilings", "Natural light", "Close to metro"],
        features: ["pets", "smoking"],
        landlord: {
          name: "Plateau Properties",
          phone: "(*************",
          email: "<EMAIL>"
        },
        availableDate: "2024-01-15",
        coordinates: { lat: 45.5276, lng: -73.5785 }
      },
      {
        id: "3",
        title: "Spacious Family Home",
        address: "789 Rue de la Paix, Quebec City, QC G1R 2L5",
        price: 2500,
        bedrooms: 3,
        bathrooms: 2,
        parking: 2,
        area: 1200,
        images: ["/api/placeholder/800/600", "/api/placeholder/800/600"],
        type: "House",
        status: "LIVE",
        description: "Perfect family home with garden and garage in quiet neighborhood.",
        amenities: ["Private garden", "Garage", "Fireplace", "Updated kitchen", "Hardwood floors"],
        features: ["pool", "mobility", "semi-furnished"],
        landlord: {
          name: "Quebec Family Homes",
          phone: "(*************",
          email: "<EMAIL>"
        },
        availableDate: "2024-03-01",
        coordinates: { lat: 46.8139, lng: -71.2080 }
      }
    ]

    // Apply filters
    let filteredProperties = mockProperties

    if (filters.query) {
      const query = filters.query.toLowerCase()
      filteredProperties = filteredProperties.filter(p => 
        p.title.toLowerCase().includes(query) || 
        p.address.toLowerCase().includes(query)
      )
    }

    if (filters.priceMin) {
      filteredProperties = filteredProperties.filter(p => p.price >= filters.priceMin!)
    }

    if (filters.priceMax) {
      filteredProperties = filteredProperties.filter(p => p.price <= filters.priceMax!)
    }

    if (filters.bedrooms) {
      filteredProperties = filteredProperties.filter(p => p.bedrooms >= filters.bedrooms!)
    }

    if (filters.propertyType && filters.propertyType !== 'all') {
      filteredProperties = filteredProperties.filter(p =>
        p.type.toLowerCase() === filters.propertyType!.toLowerCase()
      )
    }

    if (filters.features && filters.features.length > 0) {
      filteredProperties = filteredProperties.filter(p =>
        filters.features!.every(feature => p.features.includes(feature))
      )
    }

    if (filters.category && filters.category !== 'all') {
      // For now, all mock properties are residential
      // In a real app, you would filter based on property category
      if (filters.category === 'commercial') {
        filteredProperties = []
      }
    }

    const page = filters.page || 1
    const limit = filters.limit || 10
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit

    return {
      properties: filteredProperties.slice(startIndex, endIndex),
      total: filteredProperties.length,
      page,
      totalPages: Math.ceil(filteredProperties.length / limit)
    }
  }

  private getMockPropertyById(id: string): PublicProperty | null {
    const mockProperties = this.getMockProperties({}).properties
    return mockProperties.find(p => p.id === id) || null
  }

  private getMockFeaturedProperties(limit: number): PublicProperty[] {
    const mockProperties = this.getMockProperties({}).properties
    return mockProperties.slice(0, limit)
  }
}

export const publicPropertiesService = new PublicPropertiesService()
