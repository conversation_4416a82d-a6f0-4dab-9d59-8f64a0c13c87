/**
 * Notifications Service
 * 
 * Centralized service for all notification-related API calls
 */

import { api } from '../api-instance'
import type { ApiResponse, PaginatedResponse } from '@/lib/types'

// Notification Types
export interface Notification {
  id: string
  title: string
  message: string
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR'
  category: 'PAYMENT' | 'MAINTENANCE' | 'LEASE' | 'PROPERTY' | 'TENANT' | 'SYSTEM'
  isRead: boolean
  isArchived: boolean
  actionUrl?: string
  actionLabel?: string
  relatedEntityId?: string
  relatedEntityType?: 'PROPERTY' | 'TENANT' | 'LEASE' | 'PAYMENT' | 'MAINTENANCE'
  createdAt: string
  readAt?: string
}

export interface CreateNotificationRequest {
  title: string
  message: string
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR'
  category: 'PAYMENT' | 'MAINTENANCE' | 'LEASE' | 'PROPERTY' | 'TENANT' | 'SYSTEM'
  actionUrl?: string
  actionLabel?: string
  relatedEntityId?: string
  relatedEntityType?: 'PROPERTY' | 'TENANT' | 'LEASE' | 'PAYMENT' | 'MAINTENANCE'
  recipients?: string[] // User IDs to send notification to
}

export interface NotificationFilters {
  type?: string
  category?: string
  isRead?: boolean
  isArchived?: boolean
  startDate?: string
  endDate?: string
}

export interface NotificationSettings {
  emailNotifications: boolean
  pushNotifications: boolean
  smsNotifications: boolean
  categories: {
    payment: boolean
    maintenance: boolean
    lease: boolean
    property: boolean
    tenant: boolean
    system: boolean
  }
  frequency: 'IMMEDIATE' | 'DAILY' | 'WEEKLY'
}

export interface NotificationTemplate {
  id: string
  name: string
  subject: string
  body: string
  type: 'EMAIL' | 'SMS' | 'PUSH'
  category: string
  variables: string[]
  isActive: boolean
}

// Notifications Service
const notificationsService = {
  /**
   * Get all notifications with pagination and filters
   */
  async getNotifications(page: number = 1, limit: number = 10, filters?: NotificationFilters): Promise<ApiResponse<PaginatedResponse<Notification>>> {
    const params: Record<string, string> = {
      page: page.toString(),
      limit: limit.toString(),
    }

    if (filters?.type) params.type = filters.type
    if (filters?.category) params.category = filters.category
    if (filters?.isRead !== undefined) params.isRead = filters.isRead.toString()
    if (filters?.isArchived !== undefined) params.isArchived = filters.isArchived.toString()
    if (filters?.startDate) params.startDate = filters.startDate
    if (filters?.endDate) params.endDate = filters.endDate

    return api.get<PaginatedResponse<Notification>>('/notifications', { params })
  },

  /**
   * Get unread notifications count
   */
  async getUnreadCount(): Promise<ApiResponse<{ count: number }>> {
    return api.get<{ count: number }>('/notifications/unread-count')
  },

  /**
   * Get a notification by ID
   */
  async getNotificationById(id: string): Promise<ApiResponse<Notification>> {
    return api.get<Notification>(`/notifications/${id}`)
  },

  /**
   * Create a new notification
   */
  async createNotification(data: CreateNotificationRequest): Promise<ApiResponse<Notification>> {
    return api.post<Notification>('/notifications', data)
  },

  /**
   * Mark notification as read
   */
  async markAsRead(id: string): Promise<ApiResponse<Notification>> {
    return api.put<Notification>(`/notifications/${id}/read`)
  },

  /**
   * Mark notification as unread
   */
  async markAsUnread(id: string): Promise<ApiResponse<Notification>> {
    return api.put<Notification>(`/notifications/${id}/unread`)
  },

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(): Promise<ApiResponse<void>> {
    return api.put<void>('/notifications/mark-all-read')
  },

  /**
   * Archive notification
   */
  async archiveNotification(id: string): Promise<ApiResponse<Notification>> {
    return api.put<Notification>(`/notifications/${id}/archive`)
  },

  /**
   * Unarchive notification
   */
  async unarchiveNotification(id: string): Promise<ApiResponse<Notification>> {
    return api.put<Notification>(`/notifications/${id}/unarchive`)
  },

  /**
   * Delete notification
   */
  async deleteNotification(id: string): Promise<ApiResponse<void>> {
    return api.delete<void>(`/notifications/${id}`)
  },

  /**
   * Bulk mark notifications as read
   */
  async bulkMarkAsRead(ids: string[]): Promise<ApiResponse<void>> {
    return api.put<void>('/notifications/bulk-read', { ids })
  },

  /**
   * Bulk archive notifications
   */
  async bulkArchive(ids: string[]): Promise<ApiResponse<void>> {
    return api.put<void>('/notifications/bulk-archive', { ids })
  },

  /**
   * Bulk delete notifications
   */
  async bulkDelete(ids: string[]): Promise<ApiResponse<void>> {
    return api.delete<void>('/notifications/bulk-delete', { data: { ids } })
  },

  /**
   * Get notification settings
   */
  async getNotificationSettings(): Promise<ApiResponse<NotificationSettings>> {
    return api.get<NotificationSettings>('/notifications/settings')
  },

  /**
   * Update notification settings
   */
  async updateNotificationSettings(settings: Partial<NotificationSettings>): Promise<ApiResponse<NotificationSettings>> {
    return api.put<NotificationSettings>('/notifications/settings', settings)
  },

  /**
   * Get notification templates
   */
  async getNotificationTemplates(): Promise<ApiResponse<NotificationTemplate[]>> {
    return api.get<NotificationTemplate[]>('/notifications/templates')
  },

  /**
   * Create notification template
   */
  async createNotificationTemplate(template: Omit<NotificationTemplate, 'id'>): Promise<ApiResponse<NotificationTemplate>> {
    return api.post<NotificationTemplate>('/notifications/templates', template)
  },

  /**
   * Update notification template
   */
  async updateNotificationTemplate(id: string, template: Partial<NotificationTemplate>): Promise<ApiResponse<NotificationTemplate>> {
    return api.put<NotificationTemplate>(`/notifications/templates/${id}`, template)
  },

  /**
   * Delete notification template
   */
  async deleteNotificationTemplate(id: string): Promise<ApiResponse<void>> {
    return api.delete<void>(`/notifications/templates/${id}`)
  },

  /**
   * Send custom notification
   */
  async sendCustomNotification(templateId: string, recipients: string[], variables?: Record<string, string>): Promise<ApiResponse<void>> {
    return api.post<void>('/notifications/send-custom', { templateId, recipients, variables })
  },

  /**
   * Test notification delivery
   */
  async testNotification(type: 'EMAIL' | 'SMS' | 'PUSH', recipient: string): Promise<ApiResponse<void>> {
    return api.post<void>('/notifications/test', { type, recipient })
  }
}

export default notificationsService
