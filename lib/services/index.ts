/**
 * Centralized Services Architecture
 *
 * This file provides a single entry point for ALL backend API calls.
 * All components should import services from here instead of making direct API calls.
 *
 * Benefits:
 * - Centralized API management
 * - Consistent error handling
 * - Easy to mock for testing
 * - Type safety across the app
 * - Single source of truth for API endpoints
 */

// Core Services - Authentication & User Management
export * from './auth.service';
export * from './accounts.service';
export * from './verification.service';

// Property Management Services
export * from './properties.service';

// Tenant & Lease Management Services
export * from './tenants.service';
export * from './leases.service';

// Financial Services
export * from './payments.service';

// Maintenance & Support Services
export * from './maintenance.service';

// Document Management Services
export * from './documents.service';

// Analytics & Reporting Services
export * from './reports.service';
export * from './market-data.service';

// Communication Services
export * from './notifications.service';

// Utility Services
export * from './api.utils';

// Re-export the centralized API instance
export { api } from '../api-instance';

// Service Registry - for easy access to all services
export const services = {
  auth: () => import('./auth.service').then(m => m.default),
  accounts: () => import('./accounts.service').then(m => m.default),
  properties: () => import('./properties.service').then(m => m.default),
  verification: () => import('./verification.service').then(m => m.default),
  tenants: () => import('./tenants.service').then(m => m.default),
  leases: () => import('./leases.service').then(m => m.default),
  payments: () => import('./payments.service').then(m => m.default),
  maintenance: () => import('./maintenance.service').then(m => m.default),
  documents: () => import('./documents.service').then(m => m.default),
  reports: () => import('./reports.service').then(m => m.default),
  marketData: () => import('./market-data.service').then(m => m.default),
  notifications: () => import('./notifications.service').then(m => m.default),
} as const;
