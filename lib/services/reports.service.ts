/**
 * Reports Service
 * 
 * Centralized service for all reporting and analytics API calls
 */

import { api } from '../api-instance'
import type { ApiResponse } from '@/lib/types'

// Report Types
export interface FinancialReport {
  period: string
  totalRevenue: number
  totalExpenses: number
  netIncome: number
  occupancyRate: number
  collectionRate: number
  breakdown: {
    rentRevenue: number
    feeRevenue: number
    maintenanceExpenses: number
    operatingExpenses: number
  }
}

export interface PropertyPerformanceReport {
  propertyId: string
  propertyTitle: string
  occupancyRate: number
  averageRent: number
  totalRevenue: number
  maintenanceRequests: number
  tenantSatisfaction: number
  daysVacant: number
}

export interface TenantReport {
  totalTenants: number
  activeTenants: number
  newTenants: number
  tenantTurnover: number
  averageLeaseLength: number
  tenantSatisfactionScore: number
}

export interface MaintenanceReport {
  totalRequests: number
  completedRequests: number
  averageResolutionTime: number
  totalCost: number
  categoryBreakdown: Record<string, number>
  priorityBreakdown: Record<string, number>
}

export interface ReportFilters {
  startDate?: string
  endDate?: string
  propertyIds?: string[]
  tenantIds?: string[]
  categories?: string[]
}

export interface CustomReport {
  id: string
  name: string
  description: string
  type: 'FINANCIAL' | 'PROPERTY' | 'TENANT' | 'MAINTENANCE' | 'CUSTOM'
  filters: ReportFilters
  schedule?: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY'
  recipients?: string[]
  createdAt: string
}

// Reports Service
const reportsService = {
  /**
   * Get financial report
   */
  async getFinancialReport(filters?: ReportFilters): Promise<ApiResponse<FinancialReport>> {
    const params: Record<string, string> = {}
    if (filters?.startDate) params.startDate = filters.startDate
    if (filters?.endDate) params.endDate = filters.endDate
    if (filters?.propertyIds) params.propertyIds = filters.propertyIds.join(',')

    return api.get<FinancialReport>('/reports/financial', { params })
  },

  /**
   * Get property performance report
   */
  async getPropertyPerformanceReport(filters?: ReportFilters): Promise<ApiResponse<PropertyPerformanceReport[]>> {
    const params: Record<string, string> = {}
    if (filters?.startDate) params.startDate = filters.startDate
    if (filters?.endDate) params.endDate = filters.endDate
    if (filters?.propertyIds) params.propertyIds = filters.propertyIds.join(',')

    return api.get<PropertyPerformanceReport[]>('/reports/property-performance', { params })
  },

  /**
   * Get tenant report
   */
  async getTenantReport(filters?: ReportFilters): Promise<ApiResponse<TenantReport>> {
    const params: Record<string, string> = {}
    if (filters?.startDate) params.startDate = filters.startDate
    if (filters?.endDate) params.endDate = filters.endDate
    if (filters?.propertyIds) params.propertyIds = filters.propertyIds.join(',')

    return api.get<TenantReport>('/reports/tenant', { params })
  },

  /**
   * Get maintenance report
   */
  async getMaintenanceReport(filters?: ReportFilters): Promise<ApiResponse<MaintenanceReport>> {
    const params: Record<string, string> = {}
    if (filters?.startDate) params.startDate = filters.startDate
    if (filters?.endDate) params.endDate = filters.endDate
    if (filters?.propertyIds) params.propertyIds = filters.propertyIds.join(',')
    if (filters?.categories) params.categories = filters.categories.join(',')

    return api.get<MaintenanceReport>('/reports/maintenance', { params })
  },

  /**
   * Get occupancy report
   */
  async getOccupancyReport(filters?: ReportFilters): Promise<ApiResponse<any>> {
    const params: Record<string, string> = {}
    if (filters?.startDate) params.startDate = filters.startDate
    if (filters?.endDate) params.endDate = filters.endDate
    if (filters?.propertyIds) params.propertyIds = filters.propertyIds.join(',')

    return api.get<any>('/reports/occupancy', { params })
  },

  /**
   * Get rent roll report
   */
  async getRentRollReport(propertyId?: string): Promise<ApiResponse<any>> {
    const params: Record<string, string> = {}
    if (propertyId) params.propertyId = propertyId

    return api.get<any>('/reports/rent-roll', { params })
  },

  /**
   * Get cash flow report
   */
  async getCashFlowReport(filters?: ReportFilters): Promise<ApiResponse<any>> {
    const params: Record<string, string> = {}
    if (filters?.startDate) params.startDate = filters.startDate
    if (filters?.endDate) params.endDate = filters.endDate
    if (filters?.propertyIds) params.propertyIds = filters.propertyIds.join(',')

    return api.get<any>('/reports/cash-flow', { params })
  },

  /**
   * Export report to PDF
   */
  async exportReportToPDF(reportType: string, filters?: ReportFilters): Promise<ApiResponse<Blob>> {
    const params: Record<string, string> = { type: reportType }
    if (filters?.startDate) params.startDate = filters.startDate
    if (filters?.endDate) params.endDate = filters.endDate
    if (filters?.propertyIds) params.propertyIds = filters.propertyIds.join(',')

    return api.get<Blob>('/reports/export/pdf', { params })
  },

  /**
   * Export report to Excel
   */
  async exportReportToExcel(reportType: string, filters?: ReportFilters): Promise<ApiResponse<Blob>> {
    const params: Record<string, string> = { type: reportType }
    if (filters?.startDate) params.startDate = filters.startDate
    if (filters?.endDate) params.endDate = filters.endDate
    if (filters?.propertyIds) params.propertyIds = filters.propertyIds.join(',')

    return api.get<Blob>('/reports/export/excel', { params })
  },

  /**
   * Get custom reports
   */
  async getCustomReports(): Promise<ApiResponse<CustomReport[]>> {
    return api.get<CustomReport[]>('/reports/custom')
  },

  /**
   * Create custom report
   */
  async createCustomReport(data: Omit<CustomReport, 'id' | 'createdAt'>): Promise<ApiResponse<CustomReport>> {
    return api.post<CustomReport>('/reports/custom', data)
  },

  /**
   * Update custom report
   */
  async updateCustomReport(id: string, data: Partial<CustomReport>): Promise<ApiResponse<CustomReport>> {
    return api.put<CustomReport>(`/reports/custom/${id}`, data)
  },

  /**
   * Delete custom report
   */
  async deleteCustomReport(id: string): Promise<ApiResponse<void>> {
    return api.delete<void>(`/reports/custom/${id}`)
  },

  /**
   * Run custom report
   */
  async runCustomReport(id: string): Promise<ApiResponse<any>> {
    return api.post<any>(`/reports/custom/${id}/run`)
  },

  /**
   * Schedule report
   */
  async scheduleReport(reportId: string, schedule: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY', recipients: string[]): Promise<ApiResponse<void>> {
    return api.post<void>(`/reports/${reportId}/schedule`, { schedule, recipients })
  },

  /**
   * Get dashboard analytics
   */
  async getDashboardAnalytics(): Promise<ApiResponse<any>> {
    return api.get<any>('/reports/dashboard')
  }
}

export default reportsService
