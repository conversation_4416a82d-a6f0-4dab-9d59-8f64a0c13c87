import { toast } from "@/components/ui/use-toast"
import Cookies from "js-cookie"
import type { RequestOptions, ApiResponse } from "@/lib/types"

export class ApiService {
  private baseUrl: string
  private defaultHeaders: HeadersInit

  constructor(baseUrl: string = "/api") {
    this.baseUrl = baseUrl
    this.defaultHeaders = {
      "Content-Type": "application/json",
    }
  }

  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    const status = response.status

    try {
      // Check if response has content
      const contentType = response.headers.get('content-type')
      const hasJsonContent = contentType && contentType.includes('application/json')

      let data: any = null

      // Only try to parse JSON if the response has JSON content
      if (hasJsonContent && response.body) {
        const text = await response.text()
        if (text.trim()) {
          data = JSON.parse(text)
        }
      } else if (response.body) {
        // For non-JSON responses, get the text
        data = await response.text()
      }

      if (!response.ok) {
        const error = (data && typeof data === 'object' && data.error) ||
                     (typeof data === 'string' ? data : null) ||
                     response.statusText
        toast({
          title: "Error",
          description: error,
          variant: "destructive",
        })
        return { data: null as T, error, status }
      }

      // For successful responses, return the data (or null for empty responses)
      return { data: data as T, status }
    } catch (error) {
      // Only show error toast for actual parsing errors, not for successful empty responses
      if (!response.ok) {
        const errorMessage = "An unexpected error occurred"
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        })
        return { data: null as T, error: errorMessage, status }
      }

      // For successful responses that failed to parse, return success with null data
      return { data: null as T, status }
    }
  }

  private buildUrl(endpoint: string, params?: Record<string, string>): string {
    // Handle absolute URLs (starting with http:// or https://)
    const isAbsoluteUrl = this.baseUrl.startsWith('http://') || this.baseUrl.startsWith('https://');
    
    // Create the URL object based on whether baseUrl is absolute or relative
    const url = isAbsoluteUrl
      ? new URL(endpoint.startsWith('/') ? endpoint.slice(1) : endpoint, this.baseUrl)
      : new URL(`${this.baseUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`, window.location.origin);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
    }
    
    return url.toString();
  }

  // Get authentication token from cookies
  private getAuthToken(): string | null {
    // Get the token from cookies - check both possible cookie names
    const idToken = Cookies.get('id_token');
    const sessionToken = Cookies.get('session'); // This contains the access token
    return idToken || sessionToken || null;
  }

  private async request<T>(
    method: string,
    endpoint: string,
    options: RequestOptions = {},
    body?: any
  ): Promise<ApiResponse<T>> {
    try {
      const { headers = {}, params, signal } = options
      
      // Get authentication token
      const token = this.getAuthToken();
      
      // Add Authorization header if token exists
      const authHeaders: Record<string, string> = token ? { 'Authorization': `Bearer ${token}` } : {};
      
      const response = await fetch(this.buildUrl(endpoint, params), {
        method,
        headers: {
          ...this.defaultHeaders,
          ...authHeaders,
          ...headers,
        },
        body: body ? JSON.stringify(body) : undefined,
        signal,
        credentials: "include", // Include cookies in requests
      })

      return this.handleResponse<T>(response)
    } catch (error) {
      if (error instanceof Error) {
        const errorMessage = error.name === "AbortError"
          ? "Request was cancelled"
          : error.message

        // Only show toast for network errors, not for API errors (handled in handleResponse)
        if (error.name === "AbortError" || error.name === "TypeError") {
          toast({
            title: "Error",
            description: errorMessage,
            variant: "destructive",
          })
        }

        return {
          data: null as T,
          error: errorMessage,
          status: 500,
        }
      }

      return {
        data: null as T,
        error: "An unexpected error occurred",
        status: 500,
      }
    }
  }

  // GET request
  async get<T>(
    endpoint: string,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return this.request<T>("GET", endpoint, options)
  }

  // POST request
  async post<T>(
    endpoint: string,
    data: any,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return this.request<T>("POST", endpoint, options, data)
  }

  // PUT request
  async put<T>(
    endpoint: string,
    data: any,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return this.request<T>("PUT", endpoint, options, data)
  }

  // PATCH request
  async patch<T>(
    endpoint: string,
    data: any,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return this.request<T>("PATCH", endpoint, options, data)
  }

  // DELETE request
  async delete<T>(
    endpoint: string,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return this.request<T>("DELETE", endpoint, options)
  }

  // Upload files
  async upload<T>(
    endpoint: string,
    files: File | File[],
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    const formData = new FormData()
    
    if (Array.isArray(files)) {
      files.forEach((file) => formData.append("files", file))
    } else {
      formData.append("file", files)
    }

    return this.request<T>("POST", endpoint, {
      ...options,
      headers: {
        ...options?.headers,
        "Content-Type": "multipart/form-data",
      },
    }, formData)
  }
}