import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { validateRequest } from "./lib/auth.server";
import env from "./lib/env";

// Public routes that don't require authentication
const PUBLIC_ROUTES = ['/login', '/signup', '/', '/api/auth/callback', '/logout', '/api/auth/logout'];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Skip middleware for public routes
  if (PUBLIC_ROUTES.some(route => pathname === route || pathname.startsWith(`${route}/`))) {
    return NextResponse.next();
  }
  
  // Check if the route starts with /dashboard
  if (pathname.startsWith('/dashboard')) {
    try {
      // Validate the session
      const isAuthenticated = await validateRequest(request);
      
      // If not authenticated, redirect to login
      if (!isAuthenticated) {
        console.log(`Unauthorized access attempt to ${pathname}, redirecting to login`);
        const loginUrl = new URL('/login', request.url);
        loginUrl.searchParams.set('redirect', pathname);
        return NextResponse.redirect(loginUrl);
      }
      
      console.log(`Authenticated access to ${pathname}`);
    } catch (error) {
      console.error('Authentication validation error:', error);
      
      // On error, redirect to login
      const loginUrl = new URL('/login', request.url);
      return NextResponse.redirect(loginUrl);
    }
  }
  
  return NextResponse.next();
}

export const config = {
  // Configure matcher for all routes
  matcher: [
    // Match all dashboard routes
    '/dashboard/:path*',
  ]
};