#!/usr/bin/env node
/**
 * Environment Configuration Generator
 * This script creates environment-specific .env files
 * 
 * Usage:
 * node scripts/generate-env.js [environment]
 * 
 * Where environment is one of: development, staging, production
 * Default is development if not specified
 */

const fs = require('fs');
const path = require('path');

// Get environment from command line args
const args = process.argv.slice(2);
const env = args[0] || 'development';

if (!['development', 'staging', 'production'].includes(env)) {
  console.error(`Error: Invalid environment "${env}". Valid options: development, staging, production`);
  process.exit(1);
}

// Define environment-specific configurations
const configs = {
  development: {
    NEXT_PUBLIC_ENV: 'development',
    NEXT_PUBLIC_API_URL: 'http://localhost:8081',
    NEXT_PUBLIC_BASE_URL: 'http://localhost:3000',
    NEXT_PUBLIC_COGNITO_DOMAIN: 'ca-central-1mzuuzispv.auth.ca-central-1.amazoncognito.com',
    NEXT_PUBLIC_COGNITO_CLIENT_ID: '58km7m63f6tnlilr3kg4kq3ht8',
    NEXT_PUBLIC_COGNITO_CLIENT_SECRET: 'o9c6hqheg9kive9ugu2je23j1rv02qb0fjn2ql2cscv3or7n3d1',
    NEXT_PUBLIC_COGNITO_USER_POOL_ID: 'ca-central-1_MzUUZiSpV',
    NEXT_PUBLIC_COGNITO_REGION: 'ca-central-1',
  },
  staging: {
    NEXT_PUBLIC_ENV: 'staging',
    NEXT_PUBLIC_API_URL: 'https://api-staging.vestral.com',
    NEXT_PUBLIC_BASE_URL: 'https://app-staging.vestral.com',
    NEXT_PUBLIC_COGNITO_DOMAIN: 'ca-central-1mzuuzispv.auth.ca-central-1.amazoncognito.com',
    NEXT_PUBLIC_COGNITO_CLIENT_ID: '58km7m63f6tnlilr3kg4kq3ht8',
    NEXT_PUBLIC_COGNITO_CLIENT_SECRET: 'o9c6hqheg9kive9ugu2je23j1rv02qb0fjn2ql2cscv3or7n3d1',
    NEXT_PUBLIC_COGNITO_USER_POOL_ID: 'ca-central-1_MzUUZiSpV',
    NEXT_PUBLIC_COGNITO_REGION: 'ca-central-1',
  },
  production: {
    NEXT_PUBLIC_ENV: 'production',
    NEXT_PUBLIC_API_URL: 'https://api.vestral.com',
    NEXT_PUBLIC_BASE_URL: 'https://app.vestral.com',
    NEXT_PUBLIC_COGNITO_DOMAIN: 'ca-central-1mzuuzispv.auth.ca-central-1.amazoncognito.com',
    NEXT_PUBLIC_COGNITO_CLIENT_ID: '58km7m63f6tnlilr3kg4kq3ht8',
    NEXT_PUBLIC_COGNITO_CLIENT_SECRET: 'o9c6hqheg9kive9ugu2je23j1rv02qb0fjn2ql2cscv3or7n3d1',
    NEXT_PUBLIC_COGNITO_USER_POOL_ID: 'ca-central-1_MzUUZiSpV',
    NEXT_PUBLIC_COGNITO_REGION: 'ca-central-1',
  },
};

// Get the current config based on environment
const config = configs[env];

// Generate the .env file content
let fileContent = `# Environment configuration for ${env}\n`;
fileContent += `# Generated on ${new Date().toISOString()}\n\n`;

// Add each key-value pair to the file content
Object.entries(config).forEach(([key, value]) => {
  fileContent += `${key}=${value}\n`;
});

// File paths
const rootDir = path.resolve(__dirname, '..');
const envFile = path.join(rootDir, '.env.local');

// Write the file
fs.writeFileSync(envFile, fileContent);

console.log(`✅ Created .env.local file for ${env} environment`);
console.log(`📂 File location: ${envFile}`);
console.log(`\n⚠️  Note: .env.local is in .gitignore and won't be committed to Git`);
