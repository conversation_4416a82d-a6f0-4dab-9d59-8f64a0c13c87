"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { ArrowLeft, MapPin, Bed, Bath, Car, Heart, Share2, Calendar, Phone, Mail, Building2, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import Link from "next/link"
import { publicPropertiesService, PublicProperty } from "@/lib/services/public-properties.service"

export default function PropertyDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const [property, setProperty] = useState<PublicProperty | null>(null)
  const [loading, setLoading] = useState(true)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [showContactForm, setShowContactForm] = useState(false)

  // Fetch property data
  useEffect(() => {
    const fetchProperty = async () => {
      if (!params.id) return

      setLoading(true)
      try {
        const propertyData = await publicPropertiesService.getPropertyById(params.id as string)
        setProperty(propertyData)
      } catch (error) {
        console.error('Error fetching property:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchProperty()
  }, [params.id])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading property details...</p>
        </div>
      </div>
    )
  }

  if (!property) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Building2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Property Not Found</h2>
          <p className="text-gray-600 mb-6">The property you're looking for doesn't exist or has been removed.</p>
          <Link href="/properties">
            <Button className="bg-purple-600 hover:bg-purple-700">
              Back to Properties
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.back()}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <Building2 className="h-8 w-8 text-purple-600" />
              <h1 className="text-xl font-bold text-gray-900">Property Details</h1>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/login">
                <Button variant="outline" size="sm">
                  Sign In
                </Button>
              </Link>
              <Link href="/register">
                <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Image Gallery */}
            <Card className="overflow-hidden">
              <div className="relative">
                <img
                  src={property.images[currentImageIndex]}
                  alt={property.title}
                  className="w-full h-96 object-cover"
                />
                <div className="absolute top-4 left-4">
                  <Badge className="bg-green-500">Available</Badge>
                </div>
                <div className="absolute top-4 right-4 flex gap-2">
                  <Button variant="ghost" size="icon" className="bg-white/80 hover:bg-white">
                    <Heart className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="bg-white/80 hover:bg-white">
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              {property.images.length > 1 && (
                <div className="p-4">
                  <div className="flex gap-2 overflow-x-auto">
                    {property.images.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`flex-shrink-0 w-20 h-16 rounded-lg overflow-hidden border-2 ${
                          index === currentImageIndex ? 'border-purple-600' : 'border-gray-200'
                        }`}
                      >
                        <img
                          src={image}
                          alt={`View ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </Card>

            {/* Property Info */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-2xl mb-2">{property.title}</CardTitle>
                    <div className="flex items-center text-gray-600 mb-4">
                      <MapPin className="h-4 w-4 mr-2" />
                      <span>{property.address}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-purple-600">
                      ${property.price}/mo
                    </div>
                    <div className="text-sm text-gray-500">
                      Available {new Date(property.availableDate).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <Bed className="h-6 w-6 mx-auto mb-2 text-gray-600" />
                    <div className="font-semibold">{property.bedrooms}</div>
                    <div className="text-sm text-gray-500">Bedrooms</div>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <Bath className="h-6 w-6 mx-auto mb-2 text-gray-600" />
                    <div className="font-semibold">{property.bathrooms}</div>
                    <div className="text-sm text-gray-500">Bathrooms</div>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <Car className="h-6 w-6 mx-auto mb-2 text-gray-600" />
                    <div className="font-semibold">{property.parking}</div>
                    <div className="text-sm text-gray-500">Parking</div>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <Building2 className="h-6 w-6 mx-auto mb-2 text-gray-600" />
                    <div className="font-semibold">{property.type}</div>
                    <div className="text-sm text-gray-500">Property Type</div>
                  </div>
                </div>

                <Separator className="my-6" />

                <div>
                  <h3 className="text-lg font-semibold mb-3">Description</h3>
                  <p className="text-gray-600 leading-relaxed">{property.description}</p>
                </div>

                <Separator className="my-6" />

                {/* Features */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Features</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {property.isPetFriendly && (
                      <div className="flex items-center gap-2 p-2 bg-green-50 rounded-lg">
                        <span className="text-green-600">🐕</span>
                        <span className="text-sm text-green-700">Pet Friendly</span>
                      </div>
                    )}
                    {property.isSmokingAllowed && (
                      <div className="flex items-center gap-2 p-2 bg-orange-50 rounded-lg">
                        <span className="text-orange-600">🚬</span>
                        <span className="text-sm text-orange-700">Smoking Allowed</span>
                      </div>
                    )}
                    {property.isFurnished && (
                      <div className="flex items-center gap-2 p-2 bg-blue-50 rounded-lg">
                        <span className="text-blue-600">🛋️</span>
                        <span className="text-sm text-blue-700">Furnished</span>
                      </div>
                    )}
                    {property.isSemiFurnished && (
                      <div className="flex items-center gap-2 p-2 bg-blue-50 rounded-lg">
                        <span className="text-blue-600">🪑</span>
                        <span className="text-sm text-blue-700">Semi-Furnished</span>
                      </div>
                    )}
                    {property.isHasParking && (
                      <div className="flex items-center gap-2 p-2 bg-purple-50 rounded-lg">
                        <span className="text-purple-600">🚗</span>
                        <span className="text-sm text-purple-700">Parking Available</span>
                      </div>
                    )}
                    {property.isAdaptedForReducedMobility && (
                      <div className="flex items-center gap-2 p-2 bg-indigo-50 rounded-lg">
                        <span className="text-indigo-600">♿</span>
                        <span className="text-sm text-indigo-700">Accessible</span>
                      </div>
                    )}
                    {property.isHasElevator && (
                      <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                        <span className="text-gray-600">🛗</span>
                        <span className="text-sm text-gray-700">Elevator</span>
                      </div>
                    )}
                    {property.isHasPool && (
                      <div className="flex items-center gap-2 p-2 bg-cyan-50 rounded-lg">
                        <span className="text-cyan-600">🏊‍♂️</span>
                        <span className="text-sm text-cyan-700">Pool</span>
                      </div>
                    )}
                    {property.isFreeNow && (
                      <div className="flex items-center gap-2 p-2 bg-green-50 rounded-lg">
                        <span className="text-green-600">✅</span>
                        <span className="text-sm text-green-700">Available Now</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Required Verifications */}
                {property.requiredVerifications && property.requiredVerifications.length > 0 && (
                  <>
                    <Separator className="my-6" />
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Required Verifications</h3>
                      <div className="grid grid-cols-1 gap-2">
                        {property.requiredVerifications.map((verification, index) => (
                          <div key={index} className="flex items-center gap-2 p-2 bg-yellow-50 rounded-lg">
                            <span className="text-yellow-600">📋</span>
                            <span className="text-sm text-yellow-700">{verification}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}

                {/* Amenities */}
                {property.amenities && property.amenities.length > 0 && (
                  <>
                    <Separator className="my-6" />
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Amenities</h3>
                      <div className="grid grid-cols-2 gap-2">
                        {property.amenities.map((amenity, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                            <span className="text-gray-600">{amenity}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Card */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Property Owner</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-semibold text-gray-900">{property.landlord.name}</div>
                    {property.landlord.address && (
                      <div className="text-sm text-gray-500">{property.landlord.address}</div>
                    )}
                  </div>
                  {property.landlord.isVerified && (
                    <Badge className="bg-green-500">
                      ✓ Verified
                    </Badge>
                  )}
                </div>

                <div className="space-y-3">
                  <Button className="w-full bg-purple-600 hover:bg-purple-700 gap-2">
                    <Calendar className="h-4 w-4" />
                    Schedule Viewing
                  </Button>

                  <Button variant="outline" className="w-full gap-2 justify-start">
                    <Phone className="h-4 w-4" />
                    <span className="truncate">{property.landlord.phone}</span>
                  </Button>

                  <Button variant="outline" className="w-full gap-2 justify-start">
                    <Mail className="h-4 w-4" />
                    <span className="truncate">{property.landlord.email}</span>
                  </Button>
                </div>

                <div className="text-xs text-gray-500 text-center">
                  By contacting, you agree to our terms of service
                </div>
              </CardContent>
            </Card>

            {/* Map Placeholder */}
            <Card>
              <CardHeader>
                <CardTitle>Location</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-200 rounded-lg h-48 flex items-center justify-center">
                  <div className="text-center">
                    <MapPin className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500 text-sm">Interactive map</p>
                  </div>
                </div>
                <div className="mt-3 text-sm text-gray-600">
                  {property.address}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
