"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { ArrowLeft, MapPin, Bed, Bath, Heart, Share2, Calendar, Phone, Mail, Building2, Loader2, CheckCircle, Star } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import Link from "next/link"
import { publicPropertiesService, PublicProperty } from "@/lib/services/public-properties.service"
import { Logo } from "@/components/shared/logo"

export default function PropertyDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const [property, setProperty] = useState<PublicProperty | null>(null)
  const [loading, setLoading] = useState(true)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [showContactForm, setShowContactForm] = useState(false)

  // Fetch property data
  useEffect(() => {
    const fetchProperty = async () => {
      if (!params.id) return

      setLoading(true)
      try {
        const propertyData = await publicPropertiesService.getPropertyById(params.id as string)
        setProperty(propertyData)
      } catch (error) {
        console.error('Error fetching property:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchProperty()
  }, [params.id])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading property details...</p>
        </div>
      </div>
    )
  }

  if (!property) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Building2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Property Not Found</h2>
          <p className="text-gray-600 mb-6">The property you're looking for doesn't exist or has been removed.</p>
          <Link href="/properties">
            <Button className="bg-purple-600 hover:bg-purple-700">
              Back to Properties
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Enhanced Header */}
      <header className="bg-white/95 backdrop-blur-xl border-b border-gray-200/60 sticky top-0 z-50 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            {/* Logo and Navigation */}
            <div className="flex items-center gap-6">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.back()}
                className="hover:bg-purple-50 hover:text-purple-600 transition-colors duration-200"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <Logo />
              <div className="hidden md:flex items-center gap-1 text-sm text-gray-500">
                <Link href="/properties" className="hover:text-purple-600 transition-colors">
                  Properties
                </Link>
                <span className="mx-2">/</span>
                <span className="text-gray-900 font-medium">Property Details</span>
              </div>
            </div>

            {/* Right Side Actions */}
            <div className="flex items-center gap-4">
              {/* Action Buttons */}
              <div className="hidden md:flex items-center gap-2">
                <Button variant="ghost" size="sm" className="gap-2 hover:bg-red-50 hover:text-red-600">
                  <Heart className="h-4 w-4" />
                  Save
                </Button>
                <Button variant="ghost" size="sm" className="gap-2 hover:bg-blue-50 hover:text-blue-600">
                  <Share2 className="h-4 w-4" />
                  Share
                </Button>
              </div>

              {/* Auth Buttons */}
              <div className="hidden md:flex items-center gap-3">
                <Link href="/login">
                  <Button variant="ghost" size="sm" className="text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-all duration-200">
                    Sign In
                  </Button>
                </Link>
                <Link href="/register">
                  <Button size="sm" className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 px-6">
                    Get Started
                  </Button>
                </Link>
              </div>

              {/* Mobile Menu */}
              <div className="md:hidden flex items-center gap-2">
                <Button variant="ghost" size="icon">
                  <Heart className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon">
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section with Property Title */}
      <div className="bg-gradient-to-br from-purple-50 via-white to-blue-50 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-6">
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-3">{property.title}</h1>
              <div className="flex items-center text-gray-600 mb-4">
                <MapPin className="h-5 w-5 mr-2 text-purple-600" />
                <span className="text-lg">{property.address}</span>
              </div>
              <div className="flex items-center gap-4">
                <Badge className={`px-3 py-1 ${
                  property.isFreeNow ? 'bg-green-500 hover:bg-green-600' : 'bg-blue-500 hover:bg-blue-600'
                }`}>
                  {property.isFreeNow ? 'Available Now' : (() => {
                    // Parse date string like "2025-05-01, 12:00 a.m."
                    const dateStr = property.availableDate.split(',')[0]; // Get "2025-05-01"
                    const date = new Date(dateStr);
                    return `Available ${date.toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric'
                    })}`;
                  })()}
                </Badge>
                <Badge variant="outline" className="px-3 py-1">
                  {property.type}
                </Badge>
              </div>
            </div>
            <div className="text-right">
              <div className="text-4xl md:text-5xl font-bold text-purple-600 mb-2">
                ${property.price.toLocaleString()}<span className="text-2xl text-gray-500">/mo</span>
              </div>
              <div className="text-sm text-gray-500">
                Available from {new Date(property.availableDate).toLocaleDateString()}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Image Gallery */}
            <Card className="overflow-hidden shadow-lg border border-gray-200 bg-white">
              <div className="relative">
                <img
                  src={property.images[currentImageIndex]}
                  alt={property.title}
                  className="w-full h-[500px] object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                <div className="absolute bottom-4 left-4 right-4 flex items-end justify-between">
                  <div className="text-white">
                    <div className="text-sm font-medium bg-black/50 backdrop-blur-sm rounded-full px-3 py-1">
                      {currentImageIndex + 1} of {property.images.length}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="icon" className="bg-white/30 backdrop-blur-sm hover:bg-white/50 text-white">
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" className="bg-white/30 backdrop-blur-sm hover:bg-white/50 text-white">
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
              {property.images.length > 1 && (
                <div className="p-6 bg-white">
                  <div className="flex gap-3 overflow-x-auto pb-2">
                    {property.images.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`flex-shrink-0 w-24 h-18 rounded-xl overflow-hidden border-2 transition-all duration-200 ${
                          index === currentImageIndex
                            ? 'border-purple-600 shadow-lg'
                            : 'border-gray-200 hover:border-purple-300'
                        }`}
                      >
                        <img
                          src={image}
                          alt={`View ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </Card>

            {/* Property Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Bedrooms */}
              <Card className="bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-shadow duration-200">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-700">Bedrooms</h3>
                  <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                    <Bed className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
                <div className="text-4xl font-bold text-gray-900 mb-2">{property.bedrooms}</div>
                <p className="text-gray-500 text-sm">comfortable sleeping spaces</p>
              </Card>

              {/* Bathrooms */}
              <Card className="bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-shadow duration-200">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-700">Bathrooms</h3>
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <Bath className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
                <div className="text-4xl font-bold text-gray-900 mb-2">{property.bathrooms}</div>
                <p className="text-gray-500 text-sm">full bathrooms available</p>
              </Card>
            </div>

            {/* About This Property */}
            <Card className="shadow-lg border border-gray-200 bg-white">
              <CardHeader className="pb-4 bg-white">
                <CardTitle className="text-2xl text-gray-900">About This Property</CardTitle>
              </CardHeader>
              <CardContent className="bg-white">
                <p className="text-gray-700 leading-relaxed text-lg">{property.description}</p>
              </CardContent>
            </Card>

            {/* Features */}
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-900">Property Features</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {/* Pet Friendly */}
                <Card className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow duration-200">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-gray-700">Pet Friendly</h3>
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      property.isPetFriendly ? 'bg-green-100' : 'bg-gray-100'
                    }`}>
                      <span className="text-lg">🐕</span>
                    </div>
                  </div>
                  <div className={`text-2xl font-bold mb-1 ${
                    property.isPetFriendly ? 'text-green-600' : 'text-gray-400'
                  }`}>
                    {property.isPetFriendly ? 'Yes' : 'No'}
                  </div>
                  <p className="text-gray-500 text-xs">
                    {property.isPetFriendly ? 'pets welcome' : 'no pets allowed'}
                  </p>
                </Card>

                {/* Smoking Allowed */}
                <Card className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow duration-200">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-gray-700">Smoking</h3>
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      property.isSmokingAllowed ? 'bg-orange-100' : 'bg-gray-100'
                    }`}>
                      <span className="text-lg">🚬</span>
                    </div>
                  </div>
                  <div className={`text-2xl font-bold mb-1 ${
                    property.isSmokingAllowed ? 'text-orange-600' : 'text-gray-400'
                  }`}>
                    {property.isSmokingAllowed ? 'Allowed' : 'No'}
                  </div>
                  <p className="text-gray-500 text-xs">
                    {property.isSmokingAllowed ? 'smoking permitted' : 'no smoking'}
                  </p>
                </Card>

                {/* Furnished */}
                <Card className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow duration-200">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-gray-700">Furnished</h3>
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      property.isFurnished ? 'bg-blue-100' : 'bg-gray-100'
                    }`}>
                      <span className="text-lg">🛋️</span>
                    </div>
                  </div>
                  <div className={`text-2xl font-bold mb-1 ${
                    property.isFurnished ? 'text-blue-600' : 'text-gray-400'
                  }`}>
                    {property.isFurnished ? 'Yes' : 'No'}
                  </div>
                  <p className="text-gray-500 text-xs">
                    {property.isFurnished ? 'fully furnished' : 'unfurnished'}
                  </p>
                </Card>

                {/* Semi-Furnished */}
                <Card className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow duration-200">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-gray-700">Semi-Furnished</h3>
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      property.isSemiFurnished ? 'bg-indigo-100' : 'bg-gray-100'
                    }`}>
                      <span className="text-lg">🪑</span>
                    </div>
                  </div>
                  <div className={`text-2xl font-bold mb-1 ${
                    property.isSemiFurnished ? 'text-indigo-600' : 'text-gray-400'
                  }`}>
                    {property.isSemiFurnished ? 'Yes' : 'No'}
                  </div>
                  <p className="text-gray-500 text-xs">
                    {property.isSemiFurnished ? 'partially furnished' : 'not semi-furnished'}
                  </p>
                </Card>



                {/* Parking */}
                <Card className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow duration-200">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-gray-700">Parking</h3>
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      property.isHasParking ? 'bg-purple-100' : 'bg-gray-100'
                    }`}>
                      <span className="text-lg">🚗</span>
                    </div>
                  </div>
                  <div className={`text-2xl font-bold mb-1 ${
                    property.isHasParking ? 'text-purple-600' : 'text-gray-400'
                  }`}>
                    {property.isHasParking ? 'Yes' : 'No'}
                  </div>
                  <p className="text-gray-500 text-xs">
                    {property.isHasParking ? 'parking included' : 'no parking'}
                  </p>
                </Card>

                {/* Accessible */}
                <Card className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow duration-200">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-gray-700">Accessible</h3>
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      property.isAdaptedForReducedMobility ? 'bg-teal-100' : 'bg-gray-100'
                    }`}>
                      <span className="text-lg">♿</span>
                    </div>
                  </div>
                  <div className={`text-2xl font-bold mb-1 ${
                    property.isAdaptedForReducedMobility ? 'text-teal-600' : 'text-gray-400'
                  }`}>
                    {property.isAdaptedForReducedMobility ? 'Yes' : 'No'}
                  </div>
                  <p className="text-gray-500 text-xs">
                    {property.isAdaptedForReducedMobility ? 'wheelchair accessible' : 'not accessible'}
                  </p>
                </Card>

                {/* Elevator */}
                <Card className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow duration-200">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-gray-700">Elevator</h3>
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      property.isHasElevator ? 'bg-slate-100' : 'bg-gray-100'
                    }`}>
                      <span className="text-lg">🛗</span>
                    </div>
                  </div>
                  <div className={`text-2xl font-bold mb-1 ${
                    property.isHasElevator ? 'text-slate-600' : 'text-gray-400'
                  }`}>
                    {property.isHasElevator ? 'Yes' : 'No'}
                  </div>
                  <p className="text-gray-500 text-xs">
                    {property.isHasElevator ? 'elevator access' : 'no elevator'}
                  </p>
                </Card>

                {/* Swimming Pool */}
                <Card className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow duration-200">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-gray-700">Pool</h3>
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      property.isHasPool ? 'bg-cyan-100' : 'bg-gray-100'
                    }`}>
                      <span className="text-lg">🏊‍♂️</span>
                    </div>
                  </div>
                  <div className={`text-2xl font-bold mb-1 ${
                    property.isHasPool ? 'text-cyan-600' : 'text-gray-400'
                  }`}>
                    {property.isHasPool ? 'Yes' : 'No'}
                  </div>
                  <p className="text-gray-500 text-xs">
                    {property.isHasPool ? 'pool access' : 'no pool'}
                  </p>
                </Card>
              </div>
            </div>

            {/* Required Verifications */}
            {property.requiredVerifications && property.requiredVerifications.length > 0 && (
              <Card className="shadow-lg border border-gray-200 bg-white">
                <CardHeader className="pb-4 bg-white">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center">
                      <span className="text-2xl">📋</span>
                    </div>
                    <div>
                      <CardTitle className="text-2xl text-gray-900">Required Verifications</CardTitle>
                      <p className="text-sm text-gray-600 mt-1">Documents needed to apply for this property</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="bg-white">
                  <div className="space-y-4">
                    <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-white text-xs font-bold">!</span>
                        </div>
                        <div>
                          <h4 className="font-semibold text-amber-800 mb-1">Important Notice</h4>
                          <p className="text-sm text-amber-700">
                            Please prepare the following documents before applying. All verifications are mandatory for application approval.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {property.requiredVerifications.map((verification, index) => {
                        // Map verification types to appropriate icons and colors
                        const getVerificationStyle = (verification: string) => {
                          const lowerVerification = verification.toLowerCase();
                          if (lowerVerification.includes('income') || lowerVerification.includes('salary')) {
                            return { icon: '💰', color: 'green', bg: 'bg-green-50', border: 'border-green-200', text: 'text-green-800' };
                          } else if (lowerVerification.includes('credit') || lowerVerification.includes('score')) {
                            return { icon: '📊', color: 'blue', bg: 'bg-blue-50', border: 'border-blue-200', text: 'text-blue-800' };
                          } else if (lowerVerification.includes('employment') || lowerVerification.includes('job')) {
                            return { icon: '💼', color: 'purple', bg: 'bg-purple-50', border: 'border-purple-200', text: 'text-purple-800' };
                          } else if (lowerVerification.includes('reference') || lowerVerification.includes('contact')) {
                            return { icon: '👥', color: 'indigo', bg: 'bg-indigo-50', border: 'border-indigo-200', text: 'text-indigo-800' };
                          } else if (lowerVerification.includes('id') || lowerVerification.includes('identification')) {
                            return { icon: '🆔', color: 'gray', bg: 'bg-gray-50', border: 'border-gray-200', text: 'text-gray-800' };
                          } else if (lowerVerification.includes('bank') || lowerVerification.includes('statement')) {
                            return { icon: '🏦', color: 'cyan', bg: 'bg-cyan-50', border: 'border-cyan-200', text: 'text-cyan-800' };
                          } else {
                            return { icon: '📄', color: 'amber', bg: 'bg-amber-50', border: 'border-amber-200', text: 'text-amber-800' };
                          }
                        };

                        const style = getVerificationStyle(verification);

                        return (
                          <div key={index} className={`flex items-start gap-3 p-4 ${style.bg} ${style.border} rounded-lg border transition-all duration-200 hover:shadow-md`}>
                            <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm">
                              <span className="text-xl">{style.icon}</span>
                            </div>
                            <div className="flex-1">
                              <h4 className={`font-semibold ${style.text} mb-1`}>{verification}</h4>
                              <p className="text-xs text-gray-600">
                                {verification.toLowerCase().includes('income') && 'Proof of monthly income or salary'}
                                {verification.toLowerCase().includes('credit') && 'Credit report or credit score verification'}
                                {verification.toLowerCase().includes('employment') && 'Employment verification letter'}
                                {verification.toLowerCase().includes('reference') && 'Previous landlord or personal references'}
                                {verification.toLowerCase().includes('id') && 'Government-issued photo identification'}
                                {verification.toLowerCase().includes('bank') && 'Recent bank statements'}
                                {!verification.toLowerCase().includes('income') &&
                                 !verification.toLowerCase().includes('credit') &&
                                 !verification.toLowerCase().includes('employment') &&
                                 !verification.toLowerCase().includes('reference') &&
                                 !verification.toLowerCase().includes('id') &&
                                 !verification.toLowerCase().includes('bank') && 'Required documentation for application'}
                              </p>
                            </div>
                          </div>
                        );
                      })}
                    </div>

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-white text-xs">�</span>
                        </div>
                        <div>
                          <h4 className="font-semibold text-blue-800 mb-1">Application Tip</h4>
                          <p className="text-sm text-blue-700">
                            Create a free account on Vestral to complete your verifications (ID, credit check, etc.) in advance and reuse them for this and other rental applications.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Amenities */}
            {property.amenities && property.amenities.length > 0 && (
              <Card className="shadow-lg border border-gray-200 bg-white">
                <CardHeader className="pb-4 bg-white">
                  <CardTitle className="text-2xl text-gray-900">Amenities</CardTitle>
                </CardHeader>
                <CardContent className="bg-white">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {property.amenities.map((amenity, index) => (
                      <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                        <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                        <span className="text-gray-700 font-medium">{amenity}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Contact Card */}
            <Card className="shadow-lg border border-gray-200 bg-white sticky top-24">
              <CardHeader className="pb-4 bg-white">
                <CardTitle className="text-2xl text-gray-900">Contact Owner</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 bg-white">
                <div className="flex items-start gap-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xl font-bold">
                    {property.landlord.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <div className="font-bold text-lg text-gray-900">{property.landlord.name}</div>
                      {property.landlord.isVerified && (
                        <Badge className="bg-green-500 hover:bg-green-600 text-white px-2 py-1">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Verified
                        </Badge>
                      )}
                    </div>
                    {property.landlord.address && (
                      <div className="text-sm text-gray-600">{property.landlord.address}</div>
                    )}
                    <div className="flex items-center gap-1 mt-2">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <Star className="h-4 w-4 text-gray-300" />
                      <span className="text-sm text-gray-600 ml-1">4.8 (24 reviews)</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <Button className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 gap-2 py-3">
                    <Calendar className="h-5 w-5" />
                    Apply for this Property
                  </Button>

                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="text-sm font-medium text-gray-700 mb-3">Contact Information</div>
                    <div className="text-sm text-gray-600 space-y-2">
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        <span className="truncate">{property.landlord.phone}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        <span className="truncate">{property.landlord.email}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-xs text-gray-500 text-center bg-gray-50 p-3 rounded-lg">
                  By contacting, you agree to our terms of service and privacy policy
                </div>
              </CardContent>
            </Card>

            {/* Location Card */}
            <Card className="shadow-lg border border-gray-200 bg-white">
              <CardHeader className="pb-4 bg-white">
                <CardTitle className="text-2xl text-gray-900">Location</CardTitle>
              </CardHeader>
              <CardContent className="bg-white">
                <div className="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 rounded-xl h-48 flex items-center justify-center border border-gray-200 mb-4">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-3">
                      <MapPin className="h-8 w-8 text-white" />
                    </div>
                    <p className="text-gray-600 font-medium">Interactive Map</p>
                    <p className="text-sm text-gray-500">Coming Soon</p>
                  </div>
                </div>
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-start gap-3">
                    <MapPin className="h-5 w-5 text-purple-600 mt-0.5" />
                    <div>
                      <div className="font-medium text-gray-900 mb-1">Property Address</div>
                      <div className="text-gray-600">{property.address}</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
