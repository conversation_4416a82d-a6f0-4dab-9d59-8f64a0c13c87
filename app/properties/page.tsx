"use client"

import { useState, useEffect } from "react"
import { Search, MapPin, Building2, Bed, Bath, Car, Filter, Grid, Map, ChevronDown, Heart, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Slider } from "@/components/ui/slider"
import Link from "next/link"
import { publicPropertiesService, PublicProperty } from "@/lib/services/public-properties.service"

export default function PublicPropertiesPage() {
  const [properties, setProperties] = useState<PublicProperty[]>([])
  const [loading, setLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'list' | 'map'>('list')
  const [searchQuery, setSearchQuery] = useState('')
  const [priceRange, setPriceRange] = useState([0, 5000])
  const [propertyType, setPropertyType] = useState('all')
  const [bedrooms, setBedrooms] = useState('all')
  const [totalProperties, setTotalProperties] = useState(0)
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([])
  const [propertyCategory, setPropertyCategory] = useState('residential')

  // Fetch properties from API
  const fetchProperties = async () => {
    setLoading(true)
    try {
      const response = await publicPropertiesService.searchProperties({
        query: searchQuery || undefined,
        priceMin: priceRange[0],
        priceMax: priceRange[1],
        propertyType: propertyType === 'all' ? undefined : propertyType,
        bedrooms: bedrooms === 'all' ? undefined : parseInt(bedrooms),
        features: selectedFeatures.length > 0 ? selectedFeatures : undefined,
        category: propertyCategory,
        limit: 50
      })
      setProperties(response.properties)
      setTotalProperties(response.total)
    } catch (error) {
      console.error('Error fetching properties:', error)
    } finally {
      setLoading(false)
    }
  }

  // Fetch properties on component mount and when filters change
  useEffect(() => {
    fetchProperties()
  }, [searchQuery, priceRange, propertyType, bedrooms, selectedFeatures, propertyCategory])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <Building2 className="h-8 w-8 text-purple-600" />
              <h1 className="text-xl font-bold text-gray-900">Property Search</h1>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/login">
                <Button variant="outline" size="sm">
                  Sign In
                </Button>
              </Link>
              <Link href="/register">
                <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section with Search */}
      <div className="relative overflow-hidden bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600">
        {/* Background Image Overlay */}
        <div className="absolute inset-0 bg-black/20"></div>

        {/* Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center text-white mb-8">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Invite yourself to
            </h1>
            <p className="text-2xl md:text-3xl font-light mb-2">
              {totalProperties.toLocaleString()} properties
            </p>
            <p className="text-xl md:text-2xl font-light">
              in Quebec
            </p>
          </div>

          {/* Property Type Tabs */}
          <div className="flex justify-center mb-6">
            <div className="flex bg-white/10 backdrop-blur-sm rounded-lg p-1">
              <button
                onClick={() => setPropertyCategory('residential')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                  propertyCategory === 'residential'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-white hover:bg-white/10'
                }`}
              >
                🏠 Residential
              </button>
              <button
                onClick={() => setPropertyCategory('commercial')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                  propertyCategory === 'commercial'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-white hover:bg-white/10'
                }`}
              >
                🏢 Commercial
              </button>
            </div>
          </div>

          {/* Main Search Bar */}
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-lg p-2">
              <div className="flex flex-col md:flex-row gap-2">
                {/* Search Input */}
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    type="search"
                    placeholder="Search by City, Neighbourhood, Region, Address or Centre MLS"
                    className="pl-10 pr-4 py-3 border-0 focus:ring-0 text-gray-900 placeholder:text-gray-500"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                {/* For Rent Dropdown */}
                <div className="flex items-center gap-2">
                  <Select value="rent" onValueChange={() => {}}>
                    <SelectTrigger className="w-[120px] border-0 focus:ring-0">
                      <SelectValue placeholder="For rent" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="rent">For rent</SelectItem>
                      <SelectItem value="sale">For sale</SelectItem>
                    </SelectContent>
                  </Select>

                  {/* Price Filter */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="gap-2 border-0 text-gray-700">
                        Price
                        <span className="text-gray-400">$</span>
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-80 p-4 bg-white border border-gray-200 shadow-lg rounded-lg">
                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium mb-2 block">Price Range</label>
                          <Slider
                            value={priceRange}
                            onValueChange={setPriceRange}
                            max={5000}
                            min={0}
                            step={100}
                            className="w-full"
                          />
                          <div className="flex justify-between text-sm text-gray-500 mt-1">
                            <span>${priceRange[0]}</span>
                            <span>${priceRange[1]}</span>
                          </div>
                        </div>
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {/* Filters */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="gap-2 border-0 text-gray-700">
                        Filters
                        <Filter className="h-4 w-4" />
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-96 p-6 max-h-96 overflow-y-auto bg-white border border-gray-200 shadow-lg rounded-lg">
                      <div className="space-y-6">
                        {/* Property Type */}
                        <div>
                          <label className="text-sm font-medium mb-3 block text-gray-900">Property Type</label>
                          <Select value={propertyType} onValueChange={setPropertyType}>
                            <SelectTrigger>
                              <SelectValue placeholder="All Types" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All Types</SelectItem>
                              <SelectItem value="apartment">Apartment</SelectItem>
                              <SelectItem value="house">House</SelectItem>
                              <SelectItem value="studio">Studio</SelectItem>
                              <SelectItem value="condo">Condo</SelectItem>
                              <SelectItem value="townhouse">Townhouse</SelectItem>
                              <SelectItem value="duplex">Duplex</SelectItem>
                              <SelectItem value="loft">Loft</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Bedrooms */}
                        <div>
                          <label className="text-sm font-medium mb-3 block text-gray-900">Bedrooms</label>
                          <Select value={bedrooms} onValueChange={setBedrooms}>
                            <SelectTrigger>
                              <SelectValue placeholder="Any" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">Any</SelectItem>
                              <SelectItem value="1">1 Bedroom</SelectItem>
                              <SelectItem value="2">2 Bedrooms</SelectItem>
                              <SelectItem value="3">3 Bedrooms</SelectItem>
                              <SelectItem value="4">4+ Bedrooms</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Features */}
                        <div>
                          <label className="text-sm font-medium mb-3 block text-gray-900">Features</label>
                          <div className="grid grid-cols-1 gap-3">
                            {[
                              { id: 'pool', label: '🏊‍♂️ Pool' },
                              { id: 'elevator', label: '🛗 Elevator' },
                              { id: 'pets', label: '🐕 Pets Allowed' },
                              { id: 'furnished', label: '🛋️ Furnished' },
                              { id: 'semi-furnished', label: '🪑 Semi-Furnished' },
                              { id: 'mobility', label: '♿ Adapted for Reduced Mobility' },
                              { id: 'smoking', label: '🚬 Smoking Allowed' }
                            ].map((feature) => (
                              <label key={feature.id} className="flex items-center space-x-3 cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={selectedFeatures.includes(feature.id)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setSelectedFeatures([...selectedFeatures, feature.id])
                                    } else {
                                      setSelectedFeatures(selectedFeatures.filter(f => f !== feature.id))
                                    }
                                  }}
                                  className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"
                                />
                                <span className="text-sm text-gray-700">{feature.label}</span>
                              </label>
                            ))}
                          </div>
                        </div>

                        {/* Clear Filters Button */}
                        <div className="pt-4 border-t border-gray-200">
                          <Button
                            variant="outline"
                            onClick={() => {
                              setPropertyType('all')
                              setBedrooms('all')
                              setSelectedFeatures([])
                              setPriceRange([0, 5000])
                            }}
                            className="w-full border-gray-300 text-gray-700 hover:bg-gray-50"
                          >
                            Clear All Filters
                          </Button>
                        </div>
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {/* Search Button */}
                  <Button
                    onClick={fetchProperties}
                    className="bg-purple-600 hover:bg-purple-700 text-white px-6"
                  >
                    <Search className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* View Toggle */}
          <div className="flex justify-center mt-6">
            <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-lg p-1">
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className={`gap-2 ${viewMode === 'list' ? 'bg-white text-blue-600' : 'text-white hover:bg-white/10'}`}
              >
                <Grid className="h-4 w-4" />
                List
              </Button>
              <Button
                variant={viewMode === 'map' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('map')}
                className={`gap-2 ${viewMode === 'map' ? 'bg-white text-blue-600' : 'text-white hover:bg-white/10'}`}
              >
                <Map className="h-4 w-4" />
                Map
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {loading ? 'Loading...' : `${totalProperties} Properties Found`}
          </h2>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
              <p className="text-gray-600">Searching properties...</p>
            </div>
          </div>
        ) : viewMode === 'list' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {properties.length > 0 ? (
              properties.map((property) => (
                <PropertyCard key={property.id} property={property} />
              ))
            ) : (
              <div className="col-span-full text-center py-20">
                <Building2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Properties Found</h3>
                <p className="text-gray-500">Try adjusting your search criteria</p>
              </div>
            )}
          </div>
        ) : (
          <div className="bg-gray-200 rounded-lg h-[600px] flex items-center justify-center">
            <div className="text-center">
              <Map className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Map View</h3>
              <p className="text-gray-500">Interactive map will be implemented here</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

function PropertyCard({ property }: { property: PublicProperty }) {
  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-200">
      <div className="relative">
        <img
          src={property.images[0]}
          alt={property.title}
          className="w-full h-48 object-cover"
        />
        <Button
          variant="ghost"
          size="icon"
          className="absolute top-3 right-3 bg-white/80 hover:bg-white"
        >
          <Heart className="h-4 w-4" />
        </Button>
        <Badge className="absolute top-3 left-3 bg-green-500">
          Available
        </Badge>
      </div>
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold text-lg text-gray-900 line-clamp-1">
            {property.title}
          </h3>
          <span className="text-xl font-bold text-purple-600">
            ${property.price}/mo
          </span>
        </div>
        <div className="flex items-center text-gray-500 mb-3">
          <MapPin className="h-4 w-4 mr-1" />
          <span className="text-sm line-clamp-1">{property.address}</span>
        </div>
        <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
          <div className="flex items-center gap-1">
            <Bed className="h-4 w-4" />
            <span>{property.bedrooms} bed</span>
          </div>
          <div className="flex items-center gap-1">
            <Bath className="h-4 w-4" />
            <span>{property.bathrooms} bath</span>
          </div>
          {property.parking > 0 && (
            <div className="flex items-center gap-1">
              <Car className="h-4 w-4" />
              <span>{property.parking} parking</span>
            </div>
          )}
        </div>
        <p className="text-sm text-gray-600 line-clamp-2 mb-4">
          {property.description}
        </p>
        <Link href={`/properties/${property.id}`}>
          <Button className="w-full bg-purple-600 hover:bg-purple-700">
            View Details
          </Button>
        </Link>
      </CardContent>
    </Card>
  )
}
