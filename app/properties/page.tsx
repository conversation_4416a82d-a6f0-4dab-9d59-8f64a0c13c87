"use client"

import { useState, useEffect } from "react"
import { Search, MapPin, Building2, Bed, Bath, Filter, Grid, Map, ChevronDown, Heart, Share2, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Slider } from "@/components/ui/slider"
import Link from "next/link"
import { publicPropertiesService, PublicProperty } from "@/lib/services/public-properties.service"
import { Logo } from "@/components/shared/logo"
import { PropertyMap } from "@/components/ui/property-map"

export default function PublicPropertiesPage() {
  const [properties, setProperties] = useState<PublicProperty[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'list' | 'map'>('list')
  const [searchQuery, setSearchQuery] = useState('')
  const [priceRange, setPriceRange] = useState([0, 5000])
  const [propertyType, setPropertyType] = useState('all')
  const [bedrooms, setBedrooms] = useState('all')
  const [totalProperties, setTotalProperties] = useState(0)
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([])
  const [propertyCategory, setPropertyCategory] = useState('residential')
  // Fetch properties from API
  const fetchProperties = async () => {
    setLoading(true)
    setError(null)
    try {
      const filters: any = {}

      // Add filters based on current state
      if (searchQuery) filters.query = searchQuery
      if (priceRange[0] > 0 || priceRange[1] < 5000) {
        if (priceRange[0] > 0) filters.priceMin = priceRange[0]
        if (priceRange[1] < 5000) filters.priceMax = priceRange[1]
      }
      if (propertyType !== 'all') filters.propertyType = propertyType
      if (bedrooms !== 'all') filters.bedrooms = parseInt(bedrooms)
      if (selectedFeatures.length > 0) filters.features = selectedFeatures
      if (propertyCategory !== 'residential') filters.category = propertyCategory
      if (Object.keys(filters).length > 0) filters.limit = 50

      console.log('Fetching properties with filters:', filters)

      const response = await publicPropertiesService.searchProperties(filters)

      console.log('Properties response:', response)
      setProperties(response.properties)
      setTotalProperties(response.total)
    } catch (err) {
      console.error('Error fetching properties:', err)
      setError('Failed to load properties. Please try again.')
      setProperties([])
      setTotalProperties(0)
    } finally {
      setLoading(false)
    }
  }

  // Load properties on mount and when filters change
  useEffect(() => {
    fetchProperties()
  }, [searchQuery, priceRange, propertyType, bedrooms, selectedFeatures, propertyCategory])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Enhanced Header */}
      <header className="bg-white/95 backdrop-blur-xl border-b border-gray-200/60 sticky top-0 z-50 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            {/* Logo and Navigation */}
            <div className="flex items-center gap-8">
              <div className="flex items-center gap-3">
                <Logo className="group" />
                <div className="text-xs text-gray-500 font-medium">Property Search</div>
              </div>

              {/* Navigation Links */}
              <nav className="hidden md:flex items-center gap-6">
                <Link href="/properties" className="text-purple-600 font-medium border-b-2 border-purple-600 pb-1">
                  Browse Properties
                </Link>
                <Link href="/about" className="text-gray-600 hover:text-purple-600 transition-colors duration-200 font-medium">
                  About
                </Link>
                <Link href="/contact" className="text-gray-600 hover:text-purple-600 transition-colors duration-200 font-medium">
                  Contact
                </Link>
                <Link href="/help" className="text-gray-600 hover:text-purple-600 transition-colors duration-200 font-medium">
                  Help
                </Link>
              </nav>
            </div>

            {/* Right Side Actions */}
            <div className="flex items-center gap-4">
              {/* Quick Stats */}
              <div className="hidden lg:flex items-center gap-6 mr-6">
                <div className="text-center">
                  <div className="text-lg font-bold text-purple-600">{totalProperties.toLocaleString()}</div>
                  <div className="text-xs text-gray-500 font-medium">Properties</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">24/7</div>
                  <div className="text-xs text-gray-500 font-medium">Support</div>
                </div>
              </div>

              {/* Mobile Menu Button */}
              <Button variant="ghost" size="sm" className="md:hidden">
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </Button>

              {/* Auth Buttons */}
              <div className="hidden md:flex items-center gap-3">
                <Link href="/login">
                  <Button variant="ghost" size="sm" className="text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-all duration-200">
                    Sign In
                  </Button>
                </Link>
                <Link href="/register">
                  <Button size="sm" className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 px-6">
                    Get Started
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden border-t border-gray-200/60 bg-white/95 backdrop-blur-xl">
          <div className="max-w-7xl mx-auto px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Link href="/properties" className="text-purple-600 font-medium text-sm">
                  Browse
                </Link>
                <Link href="/about" className="text-gray-600 font-medium text-sm">
                  About
                </Link>
                <Link href="/contact" className="text-gray-600 font-medium text-sm">
                  Contact
                </Link>
              </div>
              <div className="flex items-center gap-2">
                <Link href="/login">
                  <Button variant="ghost" size="sm" className="text-xs">
                    Sign In
                  </Button>
                </Link>
                <Link href="/register">
                  <Button size="sm" className="bg-purple-600 hover:bg-purple-700 text-white text-xs px-4">
                    Get Started
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section with Search */}
      <div className="relative overflow-hidden bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600">
        {/* Background Image Overlay */}
        <div className="absolute inset-0 bg-black/20"></div>

        {/* Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center text-white mb-8">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Invite yourself to
            </h1>
            <p className="text-2xl md:text-3xl font-light mb-2">
              {totalProperties.toLocaleString()} properties
            </p>
            <p className="text-xl md:text-2xl font-light">
              in Quebec
            </p>
          </div>

          {/* Property Type Tabs */}
          <div className="flex justify-center mb-6">
            <div className="flex bg-white/10 backdrop-blur-sm rounded-lg p-1">
              <button
                onClick={() => setPropertyCategory('residential')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                  propertyCategory === 'residential'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-white hover:bg-white/10'
                }`}
              >
                🏠 Residential
              </button>
              <button
                onClick={() => setPropertyCategory('commercial')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                  propertyCategory === 'commercial'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-white hover:bg-white/10'
                }`}
              >
                🏢 Commercial
              </button>
            </div>
          </div>

          {/* Main Search Bar */}
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-lg p-2">
              <div className="flex flex-col md:flex-row gap-2">
                {/* Search Input */}
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    type="search"
                    placeholder="Search by City, Neighbourhood, Region, Address or Centre MLS"
                    className="pl-10 pr-4 py-3 border-0 focus:ring-0 text-gray-900 placeholder:text-gray-500"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                {/* For Rent Dropdown */}
                <div className="flex items-center gap-2">
                  <Select value="rent" onValueChange={() => {}}>
                    <SelectTrigger className="w-[120px] border-0 focus:ring-0">
                      <SelectValue placeholder="For rent" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="rent">For rent</SelectItem>
                      <SelectItem value="sale">For sale</SelectItem>
                    </SelectContent>
                  </Select>

                  {/* Price Filter */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="gap-2 border-0 text-gray-700">
                        Price
                        <span className="text-gray-400">$</span>
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-80 p-4 bg-white border border-gray-200 shadow-lg rounded-lg">
                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium mb-2 block text-gray-900">Price Range</label>
                          <Slider
                            value={priceRange}
                            onValueChange={setPriceRange}
                            max={5000}
                            min={0}
                            step={100}
                            className="w-full"
                          />
                          <div className="flex justify-between text-sm text-gray-500 mt-1">
                            <span>${priceRange[0]}</span>
                            <span>${priceRange[1]}</span>
                          </div>
                        </div>
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {/* Filters */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="gap-2 border-0 text-gray-700">
                        Filters
                        <Filter className="h-4 w-4" />
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-96 p-6 max-h-96 overflow-y-auto bg-white border border-gray-200 shadow-lg rounded-lg">
                      <div className="space-y-6">
                        {/* Property Type */}
                        <div>
                          <label className="text-sm font-medium mb-3 block text-gray-900">Property Type</label>
                          <Select value={propertyType} onValueChange={setPropertyType}>
                            <SelectTrigger>
                              <SelectValue placeholder="All Types" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All Types</SelectItem>
                              <SelectItem value="apartment">Apartment</SelectItem>
                              <SelectItem value="house">House</SelectItem>
                              <SelectItem value="studio">Studio</SelectItem>
                              <SelectItem value="condo">Condo</SelectItem>
                              <SelectItem value="townhouse">Townhouse</SelectItem>
                              <SelectItem value="duplex">Duplex</SelectItem>
                              <SelectItem value="loft">Loft</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Bedrooms */}
                        <div>
                          <label className="text-sm font-medium mb-3 block text-gray-900">Bedrooms</label>
                          <Select value={bedrooms} onValueChange={setBedrooms}>
                            <SelectTrigger>
                              <SelectValue placeholder="Any" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">Any</SelectItem>
                              <SelectItem value="1">1 Bedroom</SelectItem>
                              <SelectItem value="2">2 Bedrooms</SelectItem>
                              <SelectItem value="3">3 Bedrooms</SelectItem>
                              <SelectItem value="4">4+ Bedrooms</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Features */}
                        <div>
                          <label className="text-sm font-medium mb-3 block text-gray-900">Features</label>
                          <div className="grid grid-cols-1 gap-3">
                            {[
                              { id: 'pool', label: '🏊‍♂️ Pool' },
                              { id: 'elevator', label: '🛗 Elevator' },
                              { id: 'pets', label: '🐕 Pets Allowed' },
                              { id: 'furnished', label: '🛋️ Furnished' },
                              { id: 'semi-furnished', label: '🪑 Semi-Furnished' },
                              { id: 'mobility', label: '♿ Adapted for Reduced Mobility' },
                              { id: 'smoking', label: '🚬 Smoking Allowed' }
                            ].map((feature) => (
                              <label key={feature.id} className="flex items-center space-x-3 cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={selectedFeatures.includes(feature.id)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setSelectedFeatures([...selectedFeatures, feature.id])
                                    } else {
                                      setSelectedFeatures(selectedFeatures.filter(f => f !== feature.id))
                                    }
                                  }}
                                  className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"
                                />
                                <span className="text-sm text-gray-700">{feature.label}</span>
                              </label>
                            ))}
                          </div>
                        </div>

                        {/* Clear Filters Button */}
                        <div className="pt-4 border-t border-gray-200">
                          <Button
                            variant="outline"
                            onClick={() => {
                              setPropertyType('all')
                              setBedrooms('all')
                              setSelectedFeatures([])
                              setPriceRange([0, 5000])
                            }}
                            className="w-full border-gray-300 text-gray-700 hover:bg-gray-50"
                          >
                            Clear All Filters
                          </Button>
                        </div>
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {/* Search Button */}
                  <Button
                    onClick={() => fetchProperties()}
                    className="bg-purple-600 hover:bg-purple-700 text-white px-6"
                  >
                    <Search className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* View Toggle */}
          <div className="flex justify-center mt-6">
            <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-lg p-1">
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className={`gap-2 ${viewMode === 'list' ? 'bg-white text-blue-600' : 'text-white hover:bg-white/10'}`}
              >
                <Grid className="h-4 w-4" />
                List
              </Button>
              <Button
                variant={viewMode === 'map' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('map')}
                className={`gap-2 ${viewMode === 'map' ? 'bg-white text-blue-600' : 'text-white hover:bg-white/10'}`}
              >
                <Map className="h-4 w-4" />
                Map
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {loading ? 'Loading...' : `${totalProperties} Properties Found`}
          </h2>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-32">
            <div className="text-center">
              <div className="relative mb-6">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                  <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-lg">
                  <Building2 className="h-3.5 w-3.5 text-purple-600" />
                </div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Searching Properties</h3>
              <p className="text-gray-600">Finding the perfect properties for you...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-32">
            <div className="text-center max-w-md mx-auto">
              <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Building2 className="h-10 w-10 text-red-500" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-3">Error Loading Properties</h3>
              <p className="text-gray-600 mb-6">{error}</p>
              <Button
                onClick={() => fetchProperties()}
                className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-xl px-6 py-3"
              >
                Try Again
              </Button>
            </div>
          </div>
        ) : viewMode === 'list' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
            {properties.length > 0 ? (
              properties.map((property) => (
                <PropertyCard key={property.id} property={property} />
              ))
            ) : (
              <div className="col-span-full text-center py-20">
                <div className="max-w-md mx-auto">
                  <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Building2 className="h-10 w-10 text-gray-400" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">No Properties Found</h3>
                  <p className="text-gray-600 mb-6">We couldn't find any properties matching your search criteria. Try adjusting your filters or search terms.</p>
                  <Button
                    onClick={() => {
                      setSearchQuery('')
                      setPriceRange([0, 5000])
                      setPropertyType('all')
                      setBedrooms('all')
                      setSelectedFeatures([])
                    }}
                    variant="outline"
                    className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
                  >
                    Clear All Filters
                  </Button>
                </div>
              </div>
            )}
          </div>
        ) : (
          <PropertyMap properties={properties} />
        )}
      </div>
    </div>
  )
}

function PropertyCard({ property }: { property: PublicProperty }) {
  return (
    <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 border border-gray-200 bg-white rounded-2xl group">
      <div className="relative">
        <img
          src={property.images[0]}
          alt={property.title}
          className="w-full h-56 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Action Buttons */}
        <div className="absolute top-4 right-4 flex gap-2">
          <Button
            variant="ghost"
            size="icon"
            className="bg-white/90 hover:bg-white backdrop-blur-sm shadow-lg rounded-full w-10 h-10"
          >
            <Heart className="h-4 w-4 text-gray-700" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="bg-white/90 hover:bg-white backdrop-blur-sm shadow-lg rounded-full w-10 h-10"
          >
            <Share2 className="h-4 w-4 text-gray-700" />
          </Button>
        </div>

        {/* Availability Badge */}
        <Badge className="absolute top-4 left-4 bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-full shadow-lg">
          Available Now
        </Badge>

        {/* Property Type Badge */}
        <Badge variant="outline" className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm border-white/50 text-gray-700 px-3 py-1 rounded-full shadow-lg">
          {property.type}
        </Badge>
      </div>

      <CardContent className="p-6 space-y-4">
        {/* Header */}
        <div className="space-y-2">
          <div className="flex items-start justify-between gap-3">
            <h3 className="font-bold text-xl text-gray-900 line-clamp-2 leading-tight">
              {property.title}
            </h3>
            <div className="text-right flex-shrink-0">
              <div className="text-2xl font-bold text-purple-600">
                ${property.price.toLocaleString()}
              </div>
              <div className="text-sm text-gray-500">per month</div>
            </div>
          </div>

          <div className="flex items-center gap-2 text-gray-600">
            <MapPin className="h-4 w-4 flex-shrink-0" />
            <span className="text-sm line-clamp-1">{property.address}</span>
          </div>
        </div>

        {/* Property Stats */}
        <div className="flex items-center gap-6">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <Bed className="h-4 w-4 text-purple-600" />
            </div>
            <div>
              <div className="font-semibold text-gray-900">{property.bedrooms}</div>
              <div className="text-xs text-gray-500">Bedrooms</div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <Bath className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <div className="font-semibold text-gray-900">{property.bathrooms}</div>
              <div className="text-xs text-gray-500">Bathrooms</div>
            </div>
          </div>
        </div>

        {/* Description Preview */}
        <p className="text-gray-600 text-sm line-clamp-2 leading-relaxed">
          {property.description}
        </p>

        {/* Action Button */}
        <Link href={`/properties/${property.id}`} className="block">
          <Button className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-xl py-3">
            View Property Details
          </Button>
        </Link>
      </CardContent>
    </Card>
  )
}
