"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardT<PERSON>le, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useToast } from "@/components/ui/use-toast"
import { 
  AlertTriangle,
  Building,
  Calendar,
  Check,
  CheckCircle2,
  Clock,
  Edit,
  Info,
  Languages,
  Loader2,
  Mail,
  MapPin,
  Phone,
  Save,
  Shield,
  User,
  X
} from "lucide-react"
import Cookies from "js-cookie"
import env from "@/lib/env"
import { AddressSearch } from "@/components/maps/address-search";
import type { BusinessHour, BusinessHourResponse, ProfileData } from "@/lib/types";

// Define interfaces for the API response specific to this page
interface AccountProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  businessName: string;
  phone: string;
  website: string;
  businessType: 'COMPANY' | 'INDIVIDUAL';
  preferredLanguage: string;
  verifications: string[];
  emailConfirmed: boolean;
  idVerified: boolean;
  createdAt: string;
  updatedAt: string;
  address: string;
}

interface AccountSettingsResponse {
  accountProfile: AccountProfile;
  businessHours: BusinessHourResponse[];
  verifications: string[];
}

export default function ProfilePage() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profileData, setProfileData] = useState<AccountSettingsResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("personal");
  
  // Tab-specific edit modes
  const [personalEditMode, setPersonalEditMode] = useState(false);
  const [hoursEditMode, setHoursEditMode] = useState(false);
  const [verificationsEditMode, setVerificationsEditMode] = useState(false);
  
  // Form states
  const [profileForm, setProfileForm] = useState({
    firstName: "",
    lastName: "",
    businessName: "",
    email: "",
    phone: "",
    website: "",
    preferredLanguage: "",
    Address: ""
  });
  
  const [businessHours, setBusinessHours] = useState<BusinessHour[]>([]);
  const [showBusinessHoursMessage, setShowBusinessHoursMessage] = useState(true);
  
  // Helper function to get day index for sorting
  const getDayIndex = (day: string): number => {
    const days = ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"];
    return days.indexOf(day);
  };

  // Sort business hours by day of week
  const sortBusinessHours = <T extends { dayOfWeek: string }>(hours: T[]): T[] => {
    return [...hours].sort((a, b) => getDayIndex(a.dayOfWeek) - getDayIndex(b.dayOfWeek));
  };

  // Fetch profile data from API
  useEffect(() => {
    const fetchProfileData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // Get the token from cookies - using correct cookie names from AWS Cognito
        const idToken = Cookies.get('id_token');
        const accessToken = Cookies.get('access_token');
        const token = idToken || accessToken;
        
        if (!token) {
          setError("You are not authenticated. Please log in again.");
          router.push('/login');
          return;
        }
        
        const response = await fetch(`${env.API_URL}/accounts/settings`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (response.status === 401 || response.status === 403) {
          // Token expired or invalid
          setError("Your session has expired. Please log in again.");
          router.push('/login');
          return;
        }
        
        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }
        
        const data: AccountSettingsResponse = await response.json();
        setProfileData(data);
        
        // Initialize form states with fetched data
        setProfileForm({
          firstName: data.accountProfile.firstName || "",
          lastName: data.accountProfile.lastName || "",
          businessName: data.accountProfile.businessName || "",
          email: data.accountProfile.email || "",
          phone: data.accountProfile.phone || "",
          website: data.accountProfile.website || "",
          preferredLanguage: data.accountProfile.preferredLanguage || "en",
          Address: data.accountProfile.address ?? ""
        });
        
        // Initialize business hours - keep empty if none exist
        if (data.businessHours && data.businessHours.length > 0) {
          // Map API response format to our internal format and sort by day
          const formattedHours = sortBusinessHours(data.businessHours.map(hour => ({
            dayOfWeek: hour.dayOfWeek,
            openTime: hour.openTime.substring(0, 5), // Convert "HH:mm:ss" to "HH:mm"
            closeTime: hour.closeTime.substring(0, 5), // Convert "HH:mm:ss" to "HH:mm"
            isClosed: hour.closeAllDay === true ? true : false
          })));
          setBusinessHours(formattedHours);
          setShowBusinessHoursMessage(false);
        } else {
          // Keep business hours empty to show the informational message
          setBusinessHours([]);
          setShowBusinessHoursMessage(true);
        }
      } catch (error) {
        console.error('Error fetching profile data:', error);
        setError("Failed to load profile data. Please try again.");
        toast({
          title: "Error",
          description: "Failed to load profile data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchProfileData();
  }, [router, toast]);

  // Handle profile form input changes
  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    // Special handling for phone field to format as Canadian phone number
    if (name === 'phone') {
      // Remove all non-digit characters
      const digitsOnly = value.replace(/\D/g, '');
      
      // Format as Canadian phone number (XXX) XXX-XXXX
      let formattedPhone = '';
      if (digitsOnly.length <= 3) {
        formattedPhone = digitsOnly;
      } else if (digitsOnly.length <= 6) {
        formattedPhone = `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3)}`;
      } else {
        formattedPhone = `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3, 6)}-${digitsOnly.slice(6, 10)}`;
      }
      
      setProfileForm(prev => ({
        ...prev,
        [name]: formattedPhone
      }));
      return;
    }
    
    // Special handling for address field to ensure proper format
    if (name === 'Address' && value.length > 0) {
      // Basic address validation for Quebec addresses
      const containsPostalCode = /[A-Za-z]\d[A-Za-z]\s?\d[A-Za-z]\d/.test(value);
      const hasStreetAndCity = value.split(',').length >= 2;
      
      if (!hasStreetAndCity) {
        toast({
          title: "Address Format",
          description: "Please include street address and city",
          variant: "default",
        });
      }
      
      if (!containsPostalCode) {
        toast({
          title: "Postal Code Required",
          description: "Please include a valid Quebec postal code",
          variant: "default",
        });
      }
    }
    
    setProfileForm(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle business hour changes
  const handleBusinessHourChange = (index: number, field: keyof BusinessHour, value: string | boolean) => {
    const updatedHours = [...businessHours];
    updatedHours[index] = {
      ...updatedHours[index],
      [field]: value
    };
    setBusinessHours(updatedHours);
  };

  // Add default business hours
  const addDefaultBusinessHours = () => {
    const defaultHours = sortBusinessHours([
      { dayOfWeek: "MONDAY", openTime: "09:00", closeTime: "17:00", isClosed: false },
      { dayOfWeek: "TUESDAY", openTime: "09:00", closeTime: "17:00", isClosed: false },
      { dayOfWeek: "WEDNESDAY", openTime: "09:00", closeTime: "17:00", isClosed: false },
      { dayOfWeek: "THURSDAY", openTime: "09:00", closeTime: "17:00", isClosed: false },
      { dayOfWeek: "FRIDAY", openTime: "09:00", closeTime: "17:00", isClosed: false },
      { dayOfWeek: "SATURDAY", openTime: "10:00", closeTime: "15:00", isClosed: true },
      { dayOfWeek: "SUNDAY", openTime: "10:00", closeTime: "15:00", isClosed: true }
    ]);
    setBusinessHours(defaultHours);
    setShowBusinessHoursMessage(false);
  };
  
  // Toggle edit mode
  const toggleEditMode = (tab: 'personal' | 'hours' | 'verifications') => {
    switch (tab) {
      case 'personal':
        if (personalEditMode) {
          // Reset form to current data if canceling edit
          if (profileData) {
            setProfileForm({
              firstName: profileData.accountProfile.firstName || "",
              lastName: profileData.accountProfile.lastName || "",
              businessName: profileData.accountProfile.businessName || "",
              email: profileData.accountProfile.email || "",
              phone: profileData.accountProfile.phone || "",
              website: profileData.accountProfile.website || "",
              preferredLanguage: profileData.accountProfile.preferredLanguage || "en",
              Address: profileData.accountProfile.address ?? ""
            });
          }
        }
        setPersonalEditMode(!personalEditMode);
        break;
      case 'hours':
        if (hoursEditMode && profileData) {
          // Reset business hours to original data
          const formattedHours = profileData.businessHours.map(hour => ({
            dayOfWeek: hour.dayOfWeek,
            openTime: hour.openTime || "",
            closeTime: hour.closeTime || "",
            isClosed: hour.closeAllDay
          }));
          setBusinessHours(formattedHours);
        }
        setHoursEditMode(!hoursEditMode);
        break;
      case 'verifications':
        setVerificationsEditMode(!verificationsEditMode);
        break;
      default:
        break;
    }
  };
  
  // Function to handle business hours submission separately
  const handleBusinessHoursSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (businessHours.length === 0) return;
    
    setSaving(true);
    
    try {
      // Get the token from cookies
      const idToken = Cookies.get('id_token');
      const accessToken = Cookies.get('access_token');
      const token = idToken || accessToken;
      
      if (!token) {
        toast({
          title: "Authentication Error",
          description: "You are not authenticated. Please log in again.",
          variant: "destructive",
        });
        router.push('/login');
        return;
      }
      
      // Format business hours for API
      const businessHourItems = businessHours.map(hour => ({
        dayOfWeek: hour.dayOfWeek,
        openTime: hour.openTime,
        closeTime: hour.closeTime,
        closeAllDay: hour.isClosed === true ? true : false
      }));
      
      const response = await fetch(`${env.API_URL}/accounts/settings/business-hours`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ businessHourItems: businessHourItems })
      });
      
      if (response.status === 401 || response.status === 403) {
        toast({
          title: "Session Expired",
          description: "Your session has expired. Please log in again.",
          variant: "destructive",
        });
        router.push('/login');
        return;
      }
      
      if (!response.ok) {
        throw new Error('Failed to update business hours');
      }
      
      toast({
        title: "Business hours updated",
        description: "Your business hours have been updated successfully.",
      });
      
      // Exit edit mode after successful save
      setHoursEditMode(false);
    } catch (error) {
      console.error('Error updating business hours:', error);
      toast({
        title: "Error",
        description: "Failed to update business hours. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Main form submission handler
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    // If on the business hours tab, use the specific handler
    if (activeTab === 'hours') {
      handleBusinessHoursSubmit(event);
      return;
    }
    
    setSaving(true);
    
    try {
      // Get the token from cookies
      const idToken = Cookies.get('id_token');
      const accessToken = Cookies.get('access_token');
      const token = idToken || accessToken;
      
      if (!token) {
        toast({
          title: "Authentication Error",
          description: "You are not authenticated. Please log in again.",
          variant: "destructive",
        });
        router.push('/login');
        return;
      }
      
      // Only include changed fields in the request
      const changedFields: Record<string, any> = {};
      
      if (profileData) {
        if (profileForm.firstName !== profileData.accountProfile.firstName) {
          changedFields.firstName = profileForm.firstName;
        }
        
        if (profileForm.lastName !== profileData.accountProfile.lastName) {
          changedFields.lastName = profileForm.lastName;
        }
        
        if (profileForm.phone !== profileData.accountProfile.phone) {
          changedFields.phone = profileForm.phone;
        }
        
        if (profileForm.businessName !== profileData.accountProfile.businessName) {
          changedFields.businessName = profileForm.businessName;
        }
        
        if (profileForm.website !== profileData.accountProfile.website) {
          changedFields.siteWeb = profileForm.website; // Note: API expects 'siteWeb' field
        }
        
        if (profileForm.preferredLanguage !== profileData.accountProfile.preferredLanguage) {
          changedFields.preferredLanguage = profileForm.preferredLanguage;
        }
        
        if (profileForm.Address !== profileData.accountProfile.address) {
          changedFields.address = profileForm.Address; // Send as lowercase 'address'
        }
      }
      
      // Only send request if there are changes
      if (Object.keys(changedFields).length > 0) {
        // Send PATCH request with only changed fields
        const response = await fetch(`${env.API_URL}/accounts/settings/personal-information`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'X-HTTP-Method-Override': 'PATCH'
          },
          body: JSON.stringify(changedFields)
        });
        
        if (response.status === 401 || response.status === 403) {
          toast({
            title: "Session Expired",
            description: "Your session has expired. Please log in again.",
            variant: "destructive",
          });
          router.push('/login');
          return;
        }
        
        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }
        
        // Show success message
        toast({
          title: "Profile Updated",
          description: "Your profile has been updated successfully.",
        });
        
        // Refresh data
        const updatedResponse = await fetch(`${env.API_URL}/accounts/settings`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (updatedResponse.ok) {
          const updatedData: AccountSettingsResponse = await updatedResponse.json();
          setProfileData(updatedData);
        }
      } else {
        // No changes detected
        toast({
          title: "No Changes",
          description: "No changes were detected in your profile information.",
        });
      }
      
      // Exit edit mode after save attempt, regardless of changes
      setPersonalEditMode(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: "Update Failed",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Format day of week for display
  const formatDayOfWeek = (day: string | null | undefined) => {
    if (!day) return "Unknown";
    
    const dayMapping: Record<string, string> = {
      'MONDAY': 'Monday',
      'TUESDAY': 'Tuesday',
      'WEDNESDAY': 'Wednesday',
      'THURSDAY': 'Thursday',
      'FRIDAY': 'Friday',
      'SATURDAY': 'Saturday',
      'SUNDAY': 'Sunday'
    };
    
    return dayMapping[day] || day.charAt(0) + day.slice(1).toLowerCase();
  };
  
  // Enhanced loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50/30">
        <div className="flex flex-col items-center justify-center min-h-screen">
          <div className="relative">
            <div className="h-20 w-20 rounded-full bg-gradient-to-br from-purple-100 to-purple-200 flex items-center justify-center animate-pulse">
              <User className="h-10 w-10 text-purple-600" />
            </div>
            <div className="absolute inset-0 rounded-full border-4 border-purple-600 border-t-transparent animate-spin"></div>
          </div>
          <h3 className="text-xl font-bold text-gray-900 mt-6 mb-2">Loading Profile</h3>
          <p className="text-gray-600">Fetching your account information...</p>
        </div>
      </div>
    );
  }

  // Enhanced error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50/30">
        <div className="flex flex-col items-center justify-center min-h-screen p-4">
          <div className="relative mb-6">
            <div className="h-20 w-20 rounded-full bg-gradient-to-br from-red-100 to-red-200 flex items-center justify-center">
              <AlertTriangle className="h-10 w-10 text-red-600" />
            </div>
            <div className="absolute -top-2 -right-2 h-8 w-8 bg-red-500 rounded-full flex items-center justify-center">
              <X className="h-4 w-4 text-white" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-3">Error Loading Profile</h3>
          <p className="text-gray-600 mb-6 max-w-md text-center leading-relaxed">{error}</p>
          <Button
            onClick={() => window.location.reload()}
            className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white rounded-xl px-6 py-3 shadow-lg hover:shadow-xl transition-all duration-200"
          >
            <AlertTriangle className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50/30">
      {/* Enhanced Header with Gradient Background */}
      <div className="relative overflow-hidden bg-gradient-to-br from-purple-600 via-purple-700 to-pink-600 text-white">
        <div className="absolute inset-0 bg-gradient-to-br from-black/20 to-transparent"></div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 h-24 w-24 rounded-full bg-white/10 blur-2xl"></div>
        <div className="absolute bottom-0 left-0 -mb-8 -ml-8 h-32 w-32 rounded-full bg-white/5 blur-3xl"></div>

        <div className="relative z-10 px-4 sm:px-6 py-8">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              <div className="flex-1">
                <div className="flex items-center gap-4 mb-4">
                  <div className="p-3 bg-white/20 backdrop-blur-sm rounded-2xl">
                    <User className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-4xl sm:text-5xl font-bold tracking-tight">Account Profile</h1>
                    <p className="text-purple-100 text-lg mt-2">
                      Manage your personal information and account settings
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <div className="bg-white/15 backdrop-blur-sm rounded-2xl p-4 border border-white/30">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-white/25 rounded-xl flex items-center justify-center">
                      <Calendar className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <p className="text-white/80 text-xs font-medium uppercase tracking-wide">Member Since</p>
                      <p className="text-white text-lg font-bold">
                        {profileData?.accountProfile.createdAt ? new Date(profileData.accountProfile.createdAt).toLocaleDateString() : "Unknown"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Container */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 py-8 space-y-8">
        {/* Enhanced Profile Overview Card */}
        <Card className="border-0 bg-white/70 backdrop-blur-xl shadow-2xl rounded-3xl relative">
          <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 border-b border-purple-100/50 p-8 relative">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5"></div>
            <div className="relative z-10 flex flex-col lg:flex-row items-center gap-8">
              <div className="relative">
                <div className="h-24 w-24 lg:h-32 lg:w-32 rounded-3xl bg-gradient-to-br from-purple-600 to-purple-700 flex items-center justify-center text-white text-2xl lg:text-4xl font-bold shadow-2xl border-4 border-white group-hover:scale-110 transition-transform duration-300">
                  {profileData?.accountProfile.businessType === 'COMPANY'
                    ? profileData?.accountProfile.businessName?.charAt(0) || "C"
                    : profileData?.accountProfile.firstName?.charAt(0) || profileData?.accountProfile.lastName?.charAt(0) || "U"}
                </div>
                <div className="absolute -bottom-2 -right-2 flex gap-1">
                  {profileData?.accountProfile.emailConfirmed && (
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center border-2 border-white">
                      <CheckCircle2 className="h-3 w-3 text-white" />
                    </div>
                  )}
                  {profileData?.accountProfile.idVerified && (
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center border-2 border-white">
                      <Shield className="h-3 w-3 text-white" />
                    </div>
                  )}
                </div>
              </div>

              <div className="text-center lg:text-left flex-1">
                <CardTitle className="text-2xl lg:text-3xl font-bold text-gray-900 mb-3">
                  {profileData?.accountProfile.businessType === 'COMPANY'
                    ? profileData?.accountProfile.businessName
                    : `${profileData?.accountProfile.firstName} ${profileData?.accountProfile.lastName}`}
                </CardTitle>

                <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-4 mb-4">
                  <div className="flex items-center gap-2 bg-white/80 rounded-2xl px-4 py-2 border border-purple-200/60">
                    <Mail className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium text-gray-700 truncate max-w-[200px]">{profileData?.accountProfile.email}</span>
                  </div>

                  {profileData?.accountProfile.phone && (
                    <div className="flex items-center gap-2 bg-white/80 rounded-2xl px-4 py-2 border border-purple-200/60">
                      <Phone className="h-4 w-4 text-purple-600" />
                      <span className="text-sm font-medium text-gray-700">{profileData?.accountProfile.phone}</span>
                    </div>
                  )}
                </div>

                <div className="flex flex-wrap gap-3 justify-center lg:justify-start">
                  <span className={`inline-flex items-center rounded-2xl px-4 py-2 text-sm font-semibold border-2 ${
                    profileData?.accountProfile.businessType === 'COMPANY'
                      ? 'bg-blue-50 text-blue-700 border-blue-200'
                      : 'bg-purple-50 text-purple-700 border-purple-200'
                  }`}>
                    {profileData?.accountProfile.businessType === 'COMPANY' ? (
                      <>
                        <Building className="h-4 w-4 mr-2" />
                        Business Account
                      </>
                    ) : (
                      <>
                        <User className="h-4 w-4 mr-2" />
                        Individual Account
                      </>
                    )}
                  </span>

                  {profileData?.accountProfile.emailConfirmed && (
                    <span className="inline-flex items-center rounded-2xl bg-green-50 px-4 py-2 text-sm font-semibold text-green-700 border-2 border-green-200">
                      <CheckCircle2 className="h-4 w-4 mr-2" /> Email Verified
                    </span>
                  )}

                  {profileData?.accountProfile.idVerified && (
                    <span className="inline-flex items-center rounded-2xl bg-blue-50 px-4 py-2 text-sm font-semibold text-blue-700 border-2 border-blue-200">
                      <Shield className="h-4 w-4 mr-2" /> ID Verified
                    </span>
                  )}
                </div>
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-0">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <div className="flex justify-center p-6">
                <TabsList className="grid w-full max-w-2xl grid-cols-3 bg-slate-100/80 backdrop-blur-sm p-1 rounded-2xl border border-slate-200/60">
                  <TabsTrigger
                    value="personal"
                    className="flex items-center gap-2 rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-purple-600 transition-all duration-200 py-3 px-4 font-semibold"
                  >
                    <User className="h-4 w-4" />
                    <span className="hidden sm:inline">Personal Info</span>
                    <span className="sm:hidden">Info</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="hours"
                    className="flex items-center gap-2 rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-purple-600 transition-all duration-200 py-3 px-4 font-semibold"
                  >
                    <Clock className="h-4 w-4" />
                    <span className="hidden sm:inline">Business Hours</span>
                    <span className="sm:hidden">Hours</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="verifications"
                    className="flex items-center gap-2 rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-purple-600 transition-all duration-200 py-3 px-4 font-semibold"
                  >
                    <Shield className="h-4 w-4" />
                    <span className="hidden sm:inline">Verifications</span>
                    <span className="sm:hidden">Verify</span>
                  </TabsTrigger>
                </TabsList>
              </div>
              
              <form onSubmit={handleSubmit}>
                {/* Enhanced Personal Information Tab */}
                <TabsContent value="personal" className="p-8 space-y-8">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900">Personal Information</h3>
                      <p className="text-gray-600 mt-1">Update your personal details and contact information</p>
                    </div>
                    <Button
                      type="button"
                      variant={personalEditMode ? "outline" : "default"}
                      onClick={() => toggleEditMode('personal')}
                      className={personalEditMode
                        ? "border-red-300 text-red-600 hover:bg-red-50 rounded-xl px-6 py-3"
                        : "bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white rounded-xl px-6 py-3 shadow-lg hover:shadow-xl transition-all duration-200"
                      }
                    >
                      {personalEditMode ? (
                        <>
                          <X className="mr-2 h-4 w-4" />
                          Cancel Changes
                        </>
                      ) : (
                        <>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Information
                        </>
                      )}
                    </Button>
                  </div>

                  {profileData?.accountProfile.businessType === 'INDIVIDUAL' ? (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="firstName" className="text-gray-900 font-medium">First Name</Label>
                        {personalEditMode ? (
                          <div className="relative group">
                            <User className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 group-focus-within:text-purple-500 transition-colors duration-200" />
                            <Input
                              id="firstName"
                              name="firstName"
                              className="pl-10 pr-3 py-2 bg-gradient-to-r from-slate-50 to-white border-slate-200/60 rounded-xl focus:ring-2 focus:ring-purple-500/20 focus:border-purple-400 transition-all duration-200 shadow-sm"
                              value={profileForm.firstName}
                              onChange={handleProfileChange}
                              required
                            />
                          </div>
                        ) : (
                          <div className="flex items-center p-3 rounded-xl bg-gray-50 border border-gray-200 shadow-sm">
                            <div className="p-1.5 bg-gray-100 rounded-lg mr-3">
                              <User className="h-4 w-4 text-gray-600" />
                            </div>
                            <span className="text-gray-900 font-medium">{profileData?.accountProfile.firstName}</span>
                          </div>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="lastName" className="text-gray-900 font-medium">Last Name</Label>
                        {personalEditMode ? (
                          <div className="relative group">
                            <User className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 group-focus-within:text-purple-500 transition-colors duration-200" />
                            <Input
                              id="lastName"
                              name="lastName"
                              className="pl-10 pr-3 py-2 bg-gradient-to-r from-slate-50 to-white border-slate-200/60 rounded-xl focus:ring-2 focus:ring-purple-500/20 focus:border-purple-400 transition-all duration-200 shadow-sm"
                              value={profileForm.lastName}
                              onChange={handleProfileChange}
                              required
                            />
                          </div>
                        ) : (
                          <div className="flex items-center p-3 rounded-xl bg-gray-50 border border-gray-200 shadow-sm">
                            <div className="p-1.5 bg-gray-100 rounded-lg mr-3">
                              <User className="h-4 w-4 text-gray-600" />
                            </div>
                            <span className="text-gray-900 font-medium">{profileData?.accountProfile.lastName}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ) : null}

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {profileData?.accountProfile.businessType === 'COMPANY' ? (
                      <div className="space-y-2">
                        <Label htmlFor="businessName" className="text-gray-900 font-medium">
                          Company Name
                        </Label>
                        {personalEditMode ? (
                          <div className="relative group">
                            <Building className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 group-focus-within:text-purple-500 transition-colors duration-200" />
                            <Input
                              id="businessName"
                              name="businessName"
                              className="pl-10 pr-3 py-2 bg-gradient-to-r from-slate-50 to-white border-slate-200/60 rounded-xl focus:ring-2 focus:ring-purple-500/20 focus:border-purple-400 transition-all duration-200 shadow-sm"
                              value={profileForm.businessName}
                              onChange={handleProfileChange}
                              required
                            />
                          </div>
                        ) : (
                          <div className="flex items-center p-3 rounded-xl bg-gray-50 border border-gray-200 shadow-sm">
                            <div className="p-1.5 bg-gray-100 rounded-lg mr-3">
                              <Building className="h-4 w-4 text-gray-600" />
                            </div>
                            <span className="text-gray-900 font-medium">{profileData?.accountProfile.businessName || "Not specified"}</span>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="hidden lg:block"></div>
                    )}
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-gray-900 font-medium">Email Address</Label>
                      <div className="flex items-center p-3 rounded-xl bg-gray-50 border border-gray-200 shadow-sm">
                        <div className="p-1.5 bg-gray-100 rounded-lg mr-3">
                          <Mail className="h-4 w-4 text-gray-600" />
                        </div>
                        <div className="flex-1">
                          <span className="text-gray-900 font-medium">{profileData?.accountProfile.email}</span>
                          <div className="flex items-center gap-2 mt-1">
                            {profileData?.accountProfile.emailConfirmed ? (
                              <span className="inline-flex items-center text-xs font-medium text-green-700">
                                <CheckCircle2 className="h-3 w-3 mr-1" /> Verified
                              </span>
                            ) : (
                              <span className="inline-flex items-center text-xs font-medium text-amber-700">
                                <AlertTriangle className="h-3 w-3 mr-1" /> Not verified
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      {!profileData?.accountProfile.emailConfirmed && (
                        <p className="text-sm text-amber-600 bg-amber-50 p-2 rounded-lg border border-amber-200">
                          Email not verified. Please check your inbox for verification email.
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="text-gray-900 font-medium">Phone Number</Label>
                      {personalEditMode ? (
                        <div className="space-y-2">
                          <div className="relative group">
                            <Phone className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 group-focus-within:text-purple-500 transition-colors duration-200" />
                            <Input
                              id="phone"
                              name="phone"
                              placeholder="(XXX) XXX-XXXX"
                              maxLength={14}
                              className="pl-10 pr-3 py-2 bg-gradient-to-r from-slate-50 to-white border-slate-200/60 rounded-xl focus:ring-2 focus:ring-purple-500/20 focus:border-purple-400 transition-all duration-200 shadow-sm"
                              value={profileForm.phone}
                              onChange={handleProfileChange}
                            />
                          </div>
                          <p className="text-xs text-gray-600 bg-blue-50 p-2 rounded-lg border border-blue-200">
                            Canadian format: (XXX) XXX-XXXX
                          </p>
                        </div>
                      ) : (
                        <div className="flex items-center p-3 rounded-xl bg-gray-50 border border-gray-200 shadow-sm">
                          <div className="p-1.5 bg-gray-100 rounded-lg mr-3">
                            <Phone className="h-4 w-4 text-gray-600" />
                          </div>
                          <span className="text-gray-900 font-medium">{profileData?.accountProfile.phone || "Not specified"}</span>
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="website" className="text-gray-900 font-medium">Website</Label>
                      {personalEditMode ? (
                        <div className="relative group">
                          <Info className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 group-focus-within:text-purple-500 transition-colors duration-200" />
                          <Input
                            id="website"
                            name="website"
                            placeholder="https://www.example.com"
                            className="pl-10 pr-3 py-2 bg-gradient-to-r from-slate-50 to-white border-slate-200/60 rounded-xl focus:ring-2 focus:ring-purple-500/20 focus:border-purple-400 transition-all duration-200 shadow-sm"
                            value={profileForm.website}
                            onChange={handleProfileChange}
                          />
                        </div>
                      ) : (
                        <div className="flex items-center p-3 rounded-xl bg-gray-50 border border-gray-200 shadow-sm">
                          <div className="p-1.5 bg-gray-100 rounded-lg mr-3">
                            <Info className="h-4 w-4 text-gray-600" />
                          </div>
                          <div className="flex-1">
                            {profileData?.accountProfile.website ? (
                              <a
                                href={profileData.accountProfile.website}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-purple-600 hover:text-purple-800 font-medium hover:underline transition-colors duration-200"
                              >
                                {profileData.accountProfile.website}
                              </a>
                            ) : (
                              <span className="text-gray-500 italic">Not specified</span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="preferredLanguage" className="text-gray-900 font-medium">Preferred Language</Label>
                      {personalEditMode ? (
                        <div className="relative group">
                          <select
                            id="preferredLanguage"
                            name="preferredLanguage"
                            className="pl-10 pr-3 py-2 w-full bg-gradient-to-r from-slate-50 to-white border-slate-200/60 rounded-xl focus:ring-2 focus:ring-purple-500/20 focus:border-purple-400 transition-all duration-200 shadow-sm appearance-none cursor-pointer"
                            value={profileForm.preferredLanguage}
                            onChange={(e) => setProfileForm(prev => ({ ...prev, preferredLanguage: e.target.value }))}
                          >
                            <option value="en">English</option>
                            <option value="fr">Français</option>
                          </select>
                          <Languages className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 group-focus-within:text-purple-500 transition-colors duration-200 pointer-events-none" />
                          <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                            <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center p-3 rounded-xl bg-gray-50 border border-gray-200 shadow-sm">
                          <div className="p-1.5 bg-gray-100 rounded-lg mr-3">
                            <Languages className="h-4 w-4 text-gray-600" />
                          </div>
                          <span className="text-gray-900 font-medium">
                            {profileData?.accountProfile.preferredLanguage === 'fr' ? 'Français' : 'English'}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label className="text-gray-900 font-medium">Account Type</Label>
                      <div className="flex items-center p-3 rounded-xl bg-gradient-to-r from-slate-50 to-slate-100/50 border border-slate-200/60 shadow-sm">
                        <div className="p-1.5 bg-slate-100 rounded-lg mr-3">
                          {profileData?.accountProfile.businessType === 'COMPANY' ? (
                            <Building className="h-4 w-4 text-slate-600" />
                          ) : (
                            <User className="h-4 w-4 text-slate-600" />
                          )}
                        </div>
                        <span className="text-gray-900 font-medium">
                          {profileData?.accountProfile.businessType === 'COMPANY' ? 'Business Account' : 'Individual Account'}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2 relative z-20">
                    <Label htmlFor="Address" className="text-gray-900 font-medium">Address</Label>
                    {personalEditMode ? (
                      <div className="relative z-30">
                        <AddressSearch
                          onAddressSelect={(address) => {
                            setProfileForm(prev => ({
                              ...prev,
                              Address: address.fullAddress
                            }));
                          }}
                          defaultValue={profileForm.Address}
                          placeholder="Search for your address in Quebec..."
                          required
                          className="w-full"
                        />
                      </div>
                    ) : (
                      <div className="flex items-start p-3 rounded-xl bg-gradient-to-r from-orange-50 to-orange-100/50 border border-orange-200/60 shadow-sm">
                        <div className="p-1.5 bg-orange-100 rounded-lg mr-3 mt-0.5">
                          <MapPin className="h-4 w-4 text-orange-600 flex-shrink-0" />
                        </div>
                        <div className="flex-1">
                          {profileForm.Address ? (
                            <span className="text-gray-900 font-medium leading-relaxed">{profileForm.Address}</span>
                          ) : (
                            <span className="text-gray-500 italic">Not specified</span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    
                  </div>
                </TabsContent>
                
                {/* Business Hours Tab */}
                <TabsContent value="hours" className="p-6 space-y-6">
                  <div className="flex justify-end mb-4">
                    <Button 
                      type="button" 
                      variant="default"
                      onClick={() => toggleEditMode('hours')}
                      className={hoursEditMode 
                        ? "border-red-300 text-red-600 hover:bg-red-50" 
                        : "bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700"
                      }
                    >
                      {hoursEditMode ? (
                        <>
                          <X className="mr-2 h-4 w-4" />
                          Cancel
                        </>
                      ) : (
                        <>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Business Hours
                        </>
                      )}
                    </Button>
                  </div>
                
                  {businessHours.length === 0 ? (
                    <div className="space-y-4">
                      <Alert className="bg-blue-50 border-blue-100">
                        <Info className="h-5 w-5 text-blue-500" />
                        <AlertTitle className="text-blue-700 font-medium">No business hours set</AlertTitle>
                        <AlertDescription className="text-blue-600">
                          Setting your business hours helps customers know when they can contact you. Please add your business hours to improve customer experience.
                        </AlertDescription>
                      </Alert>
                      {hoursEditMode && (
                        <div className="flex justify-center">
                          <Button 
                            type="button" 
                            onClick={addDefaultBusinessHours}
                            className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-md"
                          >
                            <Clock className="mr-2 h-4 w-4" />
                            Add Default Business Hours
                          </Button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 gap-4">
                        {sortBusinessHours(businessHours).map((hour, index) => (
                          <div 
                            key={index} 
                            className="flex flex-col sm:flex-row sm:items-center gap-4 p-4 rounded-lg border border-gray-100 bg-white shadow-sm hover:shadow-md transition-shadow duration-200"
                          >
                            <div className="w-full sm:w-1/4">
                              <Label className="text-gray-700 flex items-center gap-2 font-medium">
                                <Clock className="h-4 w-4 text-purple-500" />
                                {formatDayOfWeek(hour.dayOfWeek)}
                              </Label>
                            </div>
                            
                            {hoursEditMode ? (
                              <>
                                <div className="flex-1 grid grid-cols-2 gap-4">
                                  <div>
                                    <Label htmlFor={`open-${index}`} className="text-gray-700 text-xs">Open</Label>
                                    <Input 
                                      id={`open-${index}`}
                                      type="time"
                                      value={hour.openTime}
                                      onChange={(e) => handleBusinessHourChange(index, 'openTime', e.target.value)}
                                      disabled={hour.isClosed}
                                      className={`border-gray-200 focus:border-purple-500 focus:ring-purple-500 ${hour.isClosed ? "opacity-50" : ""}`}
                                    />
                                  </div>
                                  <div>
                                    <Label htmlFor={`close-${index}`} className="text-gray-700 text-xs">Close</Label>
                                    <Input 
                                      id={`close-${index}`}
                                      type="time"
                                      value={hour.closeTime}
                                      onChange={(e) => handleBusinessHourChange(index, 'closeTime', e.target.value)}
                                      disabled={hour.isClosed}
                                      className={`border-gray-200 focus:border-purple-500 focus:ring-purple-500 ${hour.isClosed ? "opacity-50" : ""}`}
                                    />
                                  </div>
                                </div>
                                
                                <div className="flex items-center gap-2">
                                  <input 
                                    type="checkbox" 
                                    id={`closed-${index}`}
                                    checked={hour.isClosed}
                                    onChange={(e) => handleBusinessHourChange(index, 'isClosed', e.target.checked)}
                                    className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                                  />
                                  <Label htmlFor={`closed-${index}`} className="text-gray-700 text-sm">
                                    Closed
                                  </Label>
                                </div>
                              </>
                            ) : (
                              <div className="flex-1 flex items-center">
                                {hour.isClosed ? (
                                  <span className="px-3 py-1 bg-gray-100 rounded-full text-gray-500 text-sm font-medium italic">Closed</span>
                                ) : (
                                  <div className="flex items-center gap-2">
                                    <span className="px-3 py-1 bg-green-50 rounded-full text-green-700 text-sm font-medium">
                                      <Clock className="inline-block h-3 w-3 mr-1" /> {hour.openTime}
                                    </span>
                                    <span className="text-gray-400">to</span>
                                    <span className="px-3 py-1 bg-red-50 rounded-full text-red-700 text-sm font-medium">
                                      <Clock className="inline-block h-3 w-3 mr-1" /> {hour.closeTime}
                                    </span>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </TabsContent>
                
                {/* Verifications Tab */}
                <TabsContent value="verifications" className="p-6 space-y-6">
                  <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">Account Verifications</h3>
                  </div>
                  
                  <div className="grid grid-cols-1 gap-4">
                    <div className="p-5 rounded-lg border border-gray-100 bg-white shadow-sm">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                            <Mail className="h-5 w-5 text-purple-600" />
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">Email Verification</h4>
                            <p className="text-sm text-gray-500">Verify your email address to secure your account</p>
                          </div>
                        </div>
                        <div>
                          {profileData?.accountProfile.emailConfirmed ? (
                            <span className="inline-flex items-center rounded-full bg-green-50 px-3 py-1 text-sm font-medium text-green-700">
                              <CheckCircle2 className="h-4 w-4 mr-1" /> Verified
                            </span>
                          ) : (
                            <Button 
                              type="button" 
                              variant="outline" 
                              className="border-purple-200 text-purple-700 hover:bg-purple-50"
                            >
                              Verify Email
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-5 rounded-lg border border-gray-100 bg-white shadow-sm">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <Phone className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">Phone Verification</h4>
                            <p className="text-sm text-gray-500">Verify your phone number for account recovery</p>
                          </div>
                        </div>
                        <div>
                          {profileData?.accountProfile.phone ? (
                            <span className="inline-flex items-center rounded-full bg-green-50 px-3 py-1 text-sm font-medium text-green-700">
                              <CheckCircle2 className="h-4 w-4 mr-1" /> Added
                            </span>
                          ) : (
                            <Button 
                              type="button" 
                              variant="outline"
                              className="border-blue-200 text-blue-700 hover:bg-blue-50"
                            >
                              Add Phone
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-5 rounded-lg border border-gray-100 bg-white shadow-sm">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                            <Check className="h-5 w-5 text-amber-600" />
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">ID Verification</h4>
                            <p className="text-sm text-gray-500">Verify your identity to unlock all features</p>
                          </div>
                        </div>
                        <div>
                          {profileData?.accountProfile.idVerified ? (
                            <span className="inline-flex items-center rounded-full bg-green-50 px-3 py-1 text-sm font-medium text-green-700">
                              <CheckCircle2 className="h-4 w-4 mr-1" /> Verified
                            </span>
                          ) : (
                            <Button 
                              type="button" 
                              className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white"
                            >
                              Verify ID
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
                
                {/* Save Button for all tabs */}
                {(personalEditMode || hoursEditMode || verificationsEditMode) && (
                  <div className="sticky bottom-0 left-0 right-0 p-3 sm:p-4 bg-white border-t border-gray-100 shadow-md z-10">
                    <div className="flex flex-col xs:flex-row justify-end gap-2 xs:gap-3 max-w-4xl mx-auto">
                      <Button 
                        type="button" 
                        variant="outline" 
                        onClick={() => {
                          setPersonalEditMode(false);
                          setHoursEditMode(false);
                          setVerificationsEditMode(false);
                        }}
                        className="border-gray-200 text-gray-700 hover:bg-gray-50 order-2 xs:order-1 w-full xs:w-auto"
                        disabled={saving}
                      >
                        Cancel
                      </Button>
                      <Button 
                        type="submit" 
                        className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 order-1 xs:order-2 w-full xs:w-auto"
                        disabled={saving}
                      >
                        {saving ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="mr-2 h-4 w-4" />
                            Save Changes
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                )}
              </form>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
