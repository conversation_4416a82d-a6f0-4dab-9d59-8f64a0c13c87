"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON>, <PERSON>r<PERSON><PERSON><PERSON>, UserPlus, Clock } from "lucide-react"

const candidates = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    property: "Luxury Apartment",
    status: "Scheduled",
    date: "Tomorrow, 2:00 PM",
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    property: "Modern Villa",
    status: "Application Review",
    date: "Submitted 2 days ago",
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    property: "Downtown Penthouse",
    status: "Background Check",
    date: "In progress",
  },
]

export default function CandidatesPage() {
  return (
    <div className="space-y-8">
      <div className="flex flex-col gap-2">
        <h2 className="text-3xl font-bold tracking-tight">Candidates</h2>
        <p className="text-muted-foreground">
          Track and manage property applicants
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-card/50 backdrop-blur-sm hover:bg-card/60 transition-colors">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Candidates</CardTitle>
            <div className="h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center">
              <Users className="h-5 w-5 text-primary" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">156</div>
            <div className="flex items-center gap-1 mt-1">
              <span className="text-xs bg-emerald-500/10 text-emerald-500 px-1 rounded">+12</span>
              <span className="text-xs text-muted-foreground">this week</span>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm hover:bg-card/60 transition-colors">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">New Applications</CardTitle>
            <div className="h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center">
              <UserPlus className="h-5 w-5 text-primary" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">32</div>
            <div className="flex items-center gap-1 mt-1">
              <span className="text-xs bg-emerald-500/10 text-emerald-500 px-1 rounded">+8</span>
              <span className="text-xs text-muted-foreground">from yesterday</span>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm hover:bg-card/60 transition-colors">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <div className="h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center">
              <UserCheck className="h-5 w-5 text-primary" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">89</div>
            <div className="flex items-center gap-1 mt-1">
              <span className="text-xs bg-emerald-500/10 text-emerald-500 px-1 rounded">+15</span>
              <span className="text-xs text-muted-foreground">this month</span>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm hover:bg-card/60 transition-colors">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <div className="h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center">
              <Clock className="h-5 w-5 text-primary" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">35</div>
            <div className="flex items-center gap-1 mt-1">
              <span className="text-xs bg-rose-500/10 text-rose-500 px-1 rounded">4 urgent</span>
              <span className="text-xs text-muted-foreground">need review</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="bg-card/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle>Recent Applications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {candidates.map((candidate) => (
              <div
                key={candidate.id}
                className="flex items-center justify-between p-4 rounded-lg border border-border/50 bg-card/30 hover:bg-card/50 transition-colors"
              >
                <div className="space-y-1">
                  <h3 className="font-medium">{candidate.name}</h3>
                  <div className="text-sm text-muted-foreground">{candidate.email}</div>
                  <div className="text-sm text-primary">{candidate.property}</div>
                </div>
                <div className="text-right">
                  <div className="font-medium">{candidate.status}</div>
                  <div className="text-sm text-muted-foreground">{candidate.date}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}