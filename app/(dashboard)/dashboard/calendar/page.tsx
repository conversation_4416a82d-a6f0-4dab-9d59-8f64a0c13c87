"use client"

import { useState } from "react"
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight, Clock, MapPin, Plus } from "lucide-react"
import { format, addDays, startOfWeek, endOfWeek, eachDayOfInterval, isSameDay, isToday, addMonths, subMonths } from "date-fns"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Mock data for calendar events
const EVENTS = [
  {
    id: 1,
    title: "Property Viewing",
    description: "Showing 123 Main St to potential tenants",
    date: new Date(2025, 2, 12, 10, 0),
    endTime: new Date(2025, 2, 12, 11, 0),
    location: "123 Main St, Anytown",
    type: "viewing",
  },
  {
    id: 2,
    title: "Maintenance Visit",
    description: "Plumber fixing leak in bathroom",
    date: new Date(2025, 2, 13, 14, 0),
    endTime: new Date(2025, 2, 13, 16, 0),
    location: "456 Oak Ave, Anytown",
    type: "maintenance",
  },
  {
    id: 3,
    title: "Tenant Interview",
    description: "Meeting with prospective tenants",
    date: new Date(2025, 2, 11, 15, 30),
    endTime: new Date(2025, 2, 11, 16, 30),
    location: "Office",
    type: "meeting",
  },
  {
    id: 4,
    title: "Lease Signing",
    description: "Finalizing lease agreement with new tenants",
    date: new Date(2025, 2, 14, 11, 0),
    endTime: new Date(2025, 2, 14, 12, 0),
    location: "Office",
    type: "meeting",
  },
  {
    id: 5,
    title: "Property Inspection",
    description: "Annual inspection of 789 Pine St",
    date: new Date(2025, 2, 15, 13, 0),
    endTime: new Date(2025, 2, 15, 15, 0),
    location: "789 Pine St, Anytown",
    type: "inspection",
  },
]

type Event = typeof EVENTS[0]

export default function CalendarPage() {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState(new Date())
  
  // Get days for the monthly calendar view
  const daysInMonth = eachDayOfInterval({
    start: new Date(currentDate.getFullYear(), currentDate.getMonth(), 1),
    end: new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0),
  })
  
  // Get days for the weekly calendar view
  const startOfCurrentWeek = startOfWeek(selectedDate, { weekStartsOn: 1 })
  const endOfCurrentWeek = endOfWeek(selectedDate, { weekStartsOn: 1 })
  const daysInWeek = eachDayOfInterval({
    start: startOfCurrentWeek,
    end: endOfCurrentWeek,
  })
  
  // Filter events for the selected date
  const eventsForSelectedDate = EVENTS.filter(event => 
    isSameDay(event.date, selectedDate)
  ).sort((a, b) => a.date.getTime() - b.date.getTime())
  
  // Previous and next month handlers
  const goToPreviousMonth = () => setCurrentDate(subMonths(currentDate, 1))
  const goToNextMonth = () => setCurrentDate(addMonths(currentDate, 1))
  
  // Get event type badge color
  const getEventTypeColor = (type: string) => {
    switch (type) {
      case "viewing":
        return "bg-blue-100 text-blue-800"
      case "maintenance":
        return "bg-amber-100 text-amber-800"
      case "meeting":
        return "bg-purple-100 text-purple-800"
      case "inspection":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="container mx-auto py-6 max-w-7xl">
      <div className="flex flex-col space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">Calendar</h1>
            <p className="text-gray-500">Manage your schedule and property appointments</p>
          </div>
          <Button className="gap-2 bg-purple-600 hover:bg-purple-700 text-white">
            <Plus className="h-4 w-4" />
            Add Event
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Calendar */}
          <Card className="md:col-span-2 border-0 shadow-lg bg-white">
            <CardHeader className="pb-3 border-b bg-white">
              <div className="flex items-center justify-between">
                <CardTitle className="text-gray-900">
                  {format(currentDate, "MMMM yyyy")}
                </CardTitle>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="icon" onClick={goToPreviousMonth} className="bg-white text-gray-700 border-gray-200 hover:bg-gray-50 hover:text-gray-900">
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon" onClick={goToNextMonth} className="bg-white text-gray-700 border-gray-200 hover:bg-gray-50 hover:text-gray-900">
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-6 bg-white">
              <Tabs defaultValue="month" className="text-gray-900">
                <TabsList className="mb-4 bg-gray-100">
                  <TabsTrigger value="month" className="data-[state=active]:bg-white data-[state=active]:text-gray-900">Month</TabsTrigger>
                  <TabsTrigger value="week" className="data-[state=active]:bg-white data-[state=active]:text-gray-900">Week</TabsTrigger>
                </TabsList>
                
                <TabsContent value="month" className="text-gray-900">
                  {/* Day names header */}
                  <div className="grid grid-cols-7 mb-2">
                    {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map((day) => (
                      <div key={day} className="text-center text-sm font-medium text-gray-500">
                        {day}
                      </div>
                    ))}
                  </div>
                  
                  {/* Calendar grid */}
                  <div className="grid grid-cols-7 gap-1">
                    {/* Empty cells for days before the start of the month */}
                    {Array.from({ length: startOfWeek(daysInMonth[0], { weekStartsOn: 1 }).getDay() === 0 
                      ? 6 
                      : startOfWeek(daysInMonth[0], { weekStartsOn: 1 }).getDay() - 1 }).map((_, i) => (
                      <div key={`empty-start-${i}`} className="h-24 p-1 border border-transparent" />
                    ))}
                    
                    {/* Days of the month */}
                    {daysInMonth.map((day) => {
                      const dayEvents = EVENTS.filter(event => isSameDay(event.date, day))
                      
                      return (
                        <div
                          key={day.toString()}
                          className={cn(
                            "h-24 p-1 border rounded-md transition-colors text-gray-900",
                            isSameDay(day, selectedDate) 
                              ? "border-purple-500 bg-purple-50" 
                              : "border-gray-100 hover:border-purple-200 hover:bg-purple-50/50",
                            isToday(day) && "bg-blue-50/50"
                          )}
                          onClick={() => setSelectedDate(day)}
                        >
                          <div className="flex justify-between items-start">
                            <span 
                              className={cn(
                                "inline-flex h-6 w-6 items-center justify-center rounded-full text-sm font-medium",
                                isToday(day) && "bg-blue-600 text-white",
                                !isToday(day) && "text-gray-900"
                              )}
                            >
                              {format(day, "d")}
                            </span>
                            {dayEvents.length > 0 && (
                              <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200">
                                {dayEvents.length}
                              </Badge>
                            )}
                          </div>
                          
                          {/* Show first 2 events for the day */}
                          <div className="mt-1 space-y-1 overflow-hidden max-h-[calc(100%-1.5rem)]">
                            {dayEvents.slice(0, 2).map((event) => (
                              <div 
                                key={event.id}
                                className={cn(
                                  "text-xs px-1.5 py-0.5 rounded truncate",
                                  getEventTypeColor(event.type)
                                )}
                              >
                                {format(event.date, "h:mm a")} {event.title}
                              </div>
                            ))}
                            {dayEvents.length > 2 && (
                              <div className="text-xs text-gray-500 px-1.5">
                                +{dayEvents.length - 2} more
                              </div>
                            )}
                          </div>
                        </div>
                      )
                    })}
                    
                    {/* Empty cells for days after the end of the month */}
                    {Array.from({ length: 6 - (endOfWeek(daysInMonth[daysInMonth.length - 1], { weekStartsOn: 1 }).getDay() === 0 
                      ? 6 
                      : endOfWeek(daysInMonth[daysInMonth.length - 1], { weekStartsOn: 1 }).getDay() - 1) }).map((_, i) => (
                      <div key={`empty-end-${i}`} className="h-24 p-1 border border-transparent" />
                    ))}
                  </div>
                </TabsContent>
                
                <TabsContent value="week" className="text-gray-900">
                  <div className="flex mb-4">
                    {daysInWeek.map((day) => (
                      <div 
                        key={day.toString()}
                        className={cn(
                          "flex-1 text-center py-2 cursor-pointer rounded-md",
                          isSameDay(day, selectedDate) && "bg-purple-100 text-purple-800",
                          isToday(day) && !isSameDay(day, selectedDate) && "bg-blue-50 text-blue-800",
                          !isToday(day) && !isSameDay(day, selectedDate) && "text-gray-900"
                        )}
                        onClick={() => setSelectedDate(day)}
                      >
                        <div className="text-xs font-medium">{format(day, "EEE")}</div>
                        <div className={cn(
                          "mt-1 h-7 w-7 rounded-full flex items-center justify-center mx-auto text-sm",
                          isToday(day) && "bg-blue-600 text-white",
                          !isToday(day) && "text-gray-900"
                        )}>
                          {format(day, "d")}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="border rounded-md p-4 min-h-[400px] bg-white text-gray-900">
                    <h3 className="font-medium text-lg mb-2 text-gray-900">
                      {format(selectedDate, "EEEE, MMMM d, yyyy")}
                    </h3>
                    
                    {eventsForSelectedDate.length > 0 ? (
                      <div className="space-y-3">
                        {eventsForSelectedDate.map((event) => (
                          <div key={event.id} className="flex border rounded-md p-3 hover:bg-gray-50">
                            <div className={cn(
                              "w-1 rounded-full mr-3",
                              event.type === "viewing" && "bg-blue-500",
                              event.type === "maintenance" && "bg-amber-500",
                              event.type === "meeting" && "bg-purple-500",
                              event.type === "inspection" && "bg-green-500"
                            )} />
                            <div className="flex-1">
                              <div className="flex justify-between">
                                <h4 className="font-medium text-gray-900">{event.title}</h4>
                                <Badge className={getEventTypeColor(event.type)}>
                                  {event.type}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-600 mt-1">{event.description}</p>
                              <div className="flex items-center gap-4 mt-2">
                                <div className="flex items-center text-xs text-gray-500">
                                  <Clock className="h-3.5 w-3.5 mr-1" />
                                  {format(event.date, "h:mm a")} - {format(event.endTime, "h:mm a")}
                                </div>
                                <div className="flex items-center text-xs text-gray-500">
                                  <MapPin className="h-3.5 w-3.5 mr-1" />
                                  {event.location}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center h-[300px] text-center">
                        <CalendarIcon className="h-12 w-12 text-gray-300 mb-2" />
                        <h3 className="text-lg font-medium text-gray-900">No events scheduled</h3>
                        <p className="text-gray-500 max-w-sm mt-1">
                          There are no events scheduled for this day. Click the "Add Event" button to create a new event.
                        </p>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
          
          {/* Upcoming Events */}
          <Card className="border-0 shadow-lg bg-white">
            <CardHeader className="pb-3 border-b bg-white">
              <CardTitle className="text-gray-900">Upcoming Events</CardTitle>
              <CardDescription className="text-gray-500">Your schedule for the next 7 days</CardDescription>
            </CardHeader>
            <CardContent className="p-0 bg-white">
              <ScrollArea className="h-[500px]">
                <div className="px-4 py-2">
                  {EVENTS.filter(event => 
                    event.date >= new Date() && 
                    event.date <= addDays(new Date(), 7)
                  ).sort((a, b) => a.date.getTime() - b.date.getTime()).map((event) => (
                    <div key={event.id} className="py-3 border-b last:border-0">
                      <div className="flex items-start gap-3">
                        <div className="bg-gray-100 rounded-md p-2 text-center min-w-[3rem]">
                          <div className="text-xs font-medium text-gray-500">{format(event.date, "EEE")}</div>
                          <div className="text-lg font-bold text-gray-900">{format(event.date, "d")}</div>
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between">
                            <h4 className="font-medium text-gray-900">{event.title}</h4>
                            <Badge className={getEventTypeColor(event.type)}>
                              {event.type}
                            </Badge>
                          </div>
                          <div className="flex items-center text-xs text-gray-500 mt-1">
                            <Clock className="h-3.5 w-3.5 mr-1" />
                            {format(event.date, "h:mm a")}
                          </div>
                          <div className="flex items-center text-xs text-gray-500 mt-1">
                            <MapPin className="h-3.5 w-3.5 mr-1" />
                            {event.location}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
