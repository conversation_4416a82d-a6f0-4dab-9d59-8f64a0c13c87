"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle, CardDescription } from "@/components/ui/card"
import { Overview } from "@/components/dashboard/overview"
import { BarChart3, TrendingUp, TrendingDown, DollarSign } from "lucide-react"

export default function AnalyticsPage() {
  return (
    <div className="space-y-8">
      <div className="flex flex-col gap-2">
        <h2 className="text-3xl font-bold tracking-tight">Analytics</h2>
        <p className="text-muted-foreground">
          Track your property portfolio performance
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-card/50 backdrop-blur-sm hover:bg-card/60 transition-colors">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <div className="h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center">
              <DollarSign className="h-5 w-5 text-primary" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$482,695</div>
            <div className="flex items-center gap-1 mt-1">
              <span className="text-xs bg-emerald-500/10 text-emerald-500 px-1 rounded">+12.5%</span>
              <span className="text-xs text-muted-foreground">from last month</span>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm hover:bg-card/60 transition-colors">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Property Growth</CardTitle>
            <div className="h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center">
              <TrendingUp className="h-5 w-5 text-primary" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+24.5%</div>
            <div className="flex items-center gap-1 mt-1">
              <span className="text-xs bg-emerald-500/10 text-emerald-500 px-1 rounded">+4.3%</span>
              <span className="text-xs text-muted-foreground">vs last quarter</span>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm hover:bg-card/60 transition-colors">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Occupancy Rate</CardTitle>
            <div className="h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center">
              <BarChart3 className="h-5 w-5 text-primary" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">92%</div>
            <div className="flex items-center gap-1 mt-1">
              <span className="text-xs bg-emerald-500/10 text-emerald-500 px-1 rounded">+2%</span>
              <span className="text-xs text-muted-foreground">higher than average</span>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm hover:bg-card/60 transition-colors">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Maintenance Cost</CardTitle>
            <div className="h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center">
              <TrendingDown className="h-5 w-5 text-primary" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">-12.4%</div>
            <div className="flex items-center gap-1 mt-1">
              <span className="text-xs bg-emerald-500/10 text-emerald-500 px-1 rounded">saved</span>
              <span className="text-xs text-muted-foreground">vs last year</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4 bg-card/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>Revenue Overview</CardTitle>
            <CardDescription>
              Monthly revenue statistics for the current year
            </CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <Overview />
          </CardContent>
        </Card>
        <Card className="col-span-3 bg-card/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>Key Metrics</CardTitle>
            <CardDescription>
              Performance indicators for your portfolio
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { label: "Average Property Value", value: "$2.8M", change: "+5.2%" },
                { label: "Return on Investment", value: "18.5%", change: "+2.3%" },
                { label: "Tenant Satisfaction", value: "4.8/5", change: "+0.3" },
                { label: "Market Position", value: "Top 10%", change: "improved" },
              ].map((metric, i) => (
                <div
                  key={i}
                  className="flex items-center justify-between p-4 rounded-lg border border-border/50 bg-card/30"
                >
                  <span className="text-sm font-medium">{metric.label}</span>
                  <div className="text-right">
                    <div className="font-bold">{metric.value}</div>
                    <div className="text-xs text-emerald-500">{metric.change}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}