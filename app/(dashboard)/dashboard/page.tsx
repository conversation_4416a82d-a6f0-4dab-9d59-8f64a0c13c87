"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Overview } from "@/components/dashboard/overview"
import { RecentActivity } from "@/components/dashboard/recent-activity"
import { ArrowUpRight, Users, FolderKanban, CheckCircle2, Building2, Eye, FileText, AlertTriangle, ExternalLink } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import Cookies from "js-cookie"
import { accountsService } from "@/lib/services/accounts.service"

// Import the DashboardData interface from the service
import type { DashboardData } from "@/lib/services/accounts.service"

export default function DashboardPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Fetch dashboard data from API
  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // Get the token from cookies - using correct cookie names from AWS Cognito
        const idToken = Cookies.get('id_token');
        const accessToken = Cookies.get('access_token');
        const token = idToken || accessToken;
        
        if (!token) {
          setError("You are not authenticated. Please log in again.");
          router.push('/login');
          return;
        }
        
        const response = await accountsService.getDashboardData();
        
        if (response.status === 401 || response.status === 403) {
          // Token expired or invalid
          setError("Your session has expired. Please log in again.");
          router.push('/login');
          return;
        }
        
        if (response.error) {
          throw new Error(response.error);
        }
        
        setDashboardData(response.data);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError("Failed to load dashboard data. Please try again.");
        toast({
          title: "Error",
          description: "Failed to load dashboard data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchDashboardData();
  }, [router, toast]);

  return (
    <div className="space-y-8">
      {/* ID Verification Warning */}
      {!loading && dashboardData && !dashboardData.idVerified && (
        <Alert className="bg-amber-50 border-amber-200">
          <AlertTriangle className="h-5 w-5 text-amber-600" />
          <AlertTitle className="text-amber-800 font-medium">Identity Verification Required</AlertTitle>
          <AlertDescription className="text-amber-700">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                Your identity hasn't been verified yet. To ensure full access to all features and maintain security, 
                please complete the verification process.
              </div>
              <Button size="sm" className="whitespace-nowrap bg-purple-600 hover:bg-purple-700 text-white">
                <ExternalLink className="mr-2 h-4 w-4" />
                Verify Identity
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col gap-2">
        <h2 className="text-4xl font-bold tracking-tight text-gray-900">
          Welcome back <span className="text-primary">{dashboardData?.name || "User"}</span> 👋
        </h2>
        <p className="text-gray-500 text-lg">
          Here's what's happening with your properties today.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="overflow-hidden border-0 bg-white/50 backdrop-blur-xl shadow-xl shadow-primary/10 hover:shadow-2xl hover:shadow-primary/20 transition-all">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 border-b border-gray-100">
            <CardTitle className="text-sm font-medium text-gray-600">Active Properties</CardTitle>
            <div className="h-8 w-8 rounded-xl bg-primary/10 flex items-center justify-center">
              <Building2 className="h-5 w-5 text-primary" />
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-gray-900">{loading ? "..." : dashboardData?.activePropertiesNumber || 0}</div>
            <div className="flex items-center gap-1.5 mt-1.5">
              <span className="text-sm text-gray-500">properties currently active</span>
            </div>
          </CardContent>
        </Card>
        <Card className="overflow-hidden border-0 bg-white/50 backdrop-blur-xl shadow-xl shadow-primary/10 hover:shadow-2xl hover:shadow-primary/20 transition-all">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 border-b border-gray-100">
            <CardTitle className="text-sm font-medium text-gray-600">Property Visits</CardTitle>
            <div className="h-8 w-8 rounded-xl bg-primary/10 flex items-center justify-center">
              <Eye className="h-5 w-5 text-primary" />
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-gray-900">{loading ? "..." : dashboardData?.visitNumber || 0}</div>
            <div className="flex items-center gap-1.5 mt-1.5">
              <span className="text-sm text-gray-500">property visits scheduled</span>
            </div>
          </CardContent>
        </Card>
        <Card className="overflow-hidden border-0 bg-white/50 backdrop-blur-xl shadow-xl shadow-primary/10 hover:shadow-2xl hover:shadow-primary/20 transition-all">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 border-b border-gray-100">
            <CardTitle className="text-sm font-medium text-gray-600">Applications</CardTitle>
            <div className="h-8 w-8 rounded-xl bg-primary/10 flex items-center justify-center">
              <FileText className="h-5 w-5 text-primary" />
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-gray-900">{loading ? "..." : dashboardData?.applicationNumber || 0}</div>
            <div className="flex items-center gap-1.5 mt-1.5">
              <span className="text-sm text-gray-500">tenant applications received</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity Card */}
      <div className="max-w-2xl">
        <Card className="overflow-hidden border-0 bg-white/50 backdrop-blur-xl shadow-xl">
          <CardHeader className="border-b border-gray-100">
            <CardTitle className="text-xl font-semibold text-gray-900">Recent Activity</CardTitle>
            <CardDescription className="text-gray-500">
              Your recent property management activities
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <RecentActivity />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}