"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  LineChart, 
  TrendingUp, 
  TrendingDown,
  DollarSign, 
  Home, 
  Calendar,
  ArrowLeft,
  Download,
  Filter,
  RefreshCw,
  MapPin,
  Users
} from "lucide-react"
import Link from "next/link"

export default function MarketTrendsPage() {
  const [timeRange, setTimeRange] = useState("1year")
  const [region, setRegion] = useState("all")

  const trendData = [
    {
      title: "Average Rent Growth",
      value: "+5.2%",
      trend: "up",
      description: "Year-over-year",
      icon: DollarSign,
      color: "text-green-600"
    },
    {
      title: "Vacancy Rate Trend",
      value: "-0.8%",
      trend: "down",
      description: "Decreasing vacancy",
      icon: Home,
      color: "text-blue-600"
    },
    {
      title: "Market Activity",
      value: "+15.3%",
      trend: "up",
      description: "New listings",
      icon: Users,
      color: "text-purple-600"
    },
    {
      title: "Days on Market",
      value: "-3 days",
      trend: "down",
      description: "Faster turnover",
      icon: Calendar,
      color: "text-orange-600"
    }
  ]

  const marketInsights = [
    {
      title: "Strong Rental Demand",
      description: "Rental demand continues to outpace supply, driving rent growth across all property types.",
      impact: "Positive",
      confidence: "High"
    },
    {
      title: "Urban Core Recovery",
      description: "Downtown areas showing strong recovery with increased demand for urban living.",
      impact: "Positive",
      confidence: "Medium"
    },
    {
      title: "Interest Rate Impact",
      description: "Rising interest rates may slow purchase market but boost rental demand.",
      impact: "Mixed",
      confidence: "High"
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/dashboard/market-data">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Market Data
              </Button>
            </Link>
            <div>
              <h2 className="text-4xl font-bold tracking-tight text-gray-900">Market Trends</h2>
              <p className="text-gray-500 text-lg">
                Historical trends and market forecasting insights
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Trend Analysis Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <label className="text-sm font-medium text-gray-700 mb-2 block">Time Range</label>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="6months">Last 6 Months</SelectItem>
                  <SelectItem value="1year">Last Year</SelectItem>
                  <SelectItem value="2years">Last 2 Years</SelectItem>
                  <SelectItem value="5years">Last 5 Years</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1">
              <label className="text-sm font-medium text-gray-700 mb-2 block">Region</label>
              <Select value={region} onValueChange={setRegion}>
                <SelectTrigger>
                  <SelectValue placeholder="Select region" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Regions</SelectItem>
                  <SelectItem value="downtown">Downtown</SelectItem>
                  <SelectItem value="suburbs">Suburbs</SelectItem>
                  <SelectItem value="waterfront">Waterfront</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Trend Metrics */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {trendData.map((metric, index) => (
          <Card key={index} className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {metric.title}
              </CardTitle>
              <metric.icon className={`h-4 w-4 ${metric.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{metric.value}</div>
              <div className="flex items-center gap-1 text-xs">
                {metric.trend === "up" ? (
                  <TrendingUp className="h-3 w-3 text-green-500" />
                ) : (
                  <TrendingDown className="h-3 w-3 text-red-500" />
                )}
                <span className={metric.trend === "up" ? "text-green-600" : "text-red-600"}>
                  {metric.description}
                </span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Trend Charts */}
      <Card className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-gray-900">Market Trend Analysis</CardTitle>
          <CardDescription>
            Historical data and trend visualization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="rent" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="rent">Rent Trends</TabsTrigger>
              <TabsTrigger value="vacancy">Vacancy Rates</TabsTrigger>
              <TabsTrigger value="volume">Market Volume</TabsTrigger>
              <TabsTrigger value="forecast">Forecast</TabsTrigger>
            </TabsList>
            
            <TabsContent value="rent" className="mt-6">
              <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <LineChart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 text-lg">Rent trend chart will be displayed here</p>
                  <p className="text-sm text-gray-400">Integration with charting library needed</p>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="vacancy" className="mt-6">
              <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <Home className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 text-lg">Vacancy rate chart will be displayed here</p>
                  <p className="text-sm text-gray-400">Integration with charting library needed</p>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="volume" className="mt-6">
              <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 text-lg">Market volume chart will be displayed here</p>
                  <p className="text-sm text-gray-400">Integration with charting library needed</p>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="forecast" className="mt-6">
              <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <TrendingUp className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 text-lg">Market forecast will be displayed here</p>
                  <p className="text-sm text-gray-400">Integration with forecasting model needed</p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Market Insights */}
      <Card className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-gray-900">Market Insights</CardTitle>
          <CardDescription>
            Key insights and analysis from current market trends
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {marketInsights.map((insight, index) => (
              <div key={index} className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 mb-2">{insight.title}</h4>
                    <p className="text-gray-600 text-sm">{insight.description}</p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <Badge 
                      className={
                        insight.impact === "Positive" 
                          ? "bg-green-100 text-green-800" 
                          : insight.impact === "Mixed"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                      }
                    >
                      {insight.impact}
                    </Badge>
                    <Badge variant="outline">
                      {insight.confidence} Confidence
                    </Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
