"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  PieChart, 
  TrendingUp, 
  TrendingDown,
  DollarSign, 
  Home, 
  MapPin,
  ArrowLeft,
  Download,
  Filter,
  RefreshCw,
  BarChart3,
  Users,
  Calendar
} from "lucide-react"
import Link from "next/link"

export default function ComparativeAnalysisPage() {
  const [comparisonType, setComparisonType] = useState("properties")
  const [selectedProperties, setSelectedProperties] = useState<string[]>(["prop1", "prop2"])

  const properties = [
    { id: "prop1", name: "Downtown Condo A", address: "123 Main St", type: "Condo" },
    { id: "prop2", name: "Suburban House B", address: "456 Oak Ave", type: "House" },
    { id: "prop3", name: "Waterfront Loft C", address: "789 Bay St", type: "Loft" },
    { id: "prop4", name: "Urban Studio D", address: "321 City Blvd", type: "Studio" }
  ]

  const comparisonData = [
    {
      metric: "Monthly Rent",
      prop1: "$2,500",
      prop2: "$3,200",
      prop3: "$2,800",
      winner: "prop2"
    },
    {
      metric: "Occupancy Rate",
      prop1: "95%",
      prop2: "88%",
      prop3: "92%",
      winner: "prop1"
    },
    {
      metric: "ROI",
      prop1: "8.5%",
      prop2: "7.2%",
      prop3: "9.1%",
      winner: "prop3"
    },
    {
      metric: "Days Vacant/Year",
      prop1: "18",
      prop2: "44",
      prop3: "29",
      winner: "prop1"
    },
    {
      metric: "Maintenance Cost",
      prop1: "$450",
      prop2: "$680",
      prop3: "$520",
      winner: "prop1"
    }
  ]

  const marketComparison = [
    {
      region: "Downtown",
      avgRent: "$2,650",
      vacancy: "3.2%",
      growth: "+6.8%",
      trend: "up"
    },
    {
      region: "Suburbs",
      avgRent: "$2,200",
      vacancy: "4.1%",
      growth: "+4.2%",
      trend: "up"
    },
    {
      region: "Waterfront",
      avgRent: "$3,100",
      vacancy: "2.8%",
      growth: "+8.1%",
      trend: "up"
    }
  ]

  const handlePropertySelection = (propertyId: string, checked: boolean) => {
    if (checked) {
      setSelectedProperties([...selectedProperties, propertyId])
    } else {
      setSelectedProperties(selectedProperties.filter(id => id !== propertyId))
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/dashboard/market-data">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Market Data
              </Button>
            </Link>
            <div>
              <h2 className="text-4xl font-bold tracking-tight text-gray-900">Comparative Analysis</h2>
              <p className="text-gray-500 text-lg">
                Compare properties, markets, and performance metrics
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Comparison Type Selection */}
      <Card className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Comparison Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Comparison Type</label>
              <Select value={comparisonType} onValueChange={setComparisonType}>
                <SelectTrigger className="w-64">
                  <SelectValue placeholder="Select comparison type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="properties">Property Comparison</SelectItem>
                  <SelectItem value="markets">Market Comparison</SelectItem>
                  <SelectItem value="performance">Performance Analysis</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {comparisonType === "properties" && (
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">Select Properties to Compare</label>
                <div className="grid grid-cols-2 gap-4">
                  {properties.map((property) => (
                    <div key={property.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={property.id}
                        checked={selectedProperties.includes(property.id)}
                        onCheckedChange={(checked) => handlePropertySelection(property.id, checked as boolean)}
                      />
                      <label htmlFor={property.id} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                        {property.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Property Comparison Table */}
      {comparisonType === "properties" && (
        <Card className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              Property Performance Comparison
            </CardTitle>
            <CardDescription>
              Side-by-side comparison of selected properties
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Metric</th>
                    {selectedProperties.slice(0, 3).map((propId) => {
                      const property = properties.find(p => p.id === propId)
                      return (
                        <th key={propId} className="text-center py-3 px-4 font-semibold text-gray-900">
                          {property?.name}
                        </th>
                      )
                    })}
                  </tr>
                </thead>
                <tbody>
                  {comparisonData.map((row, index) => (
                    <tr key={index} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4 font-medium text-gray-900">{row.metric}</td>
                      <td className={`text-center py-3 px-4 ${row.winner === 'prop1' ? 'bg-green-50 text-green-800 font-semibold' : ''}`}>
                        {row.prop1}
                      </td>
                      <td className={`text-center py-3 px-4 ${row.winner === 'prop2' ? 'bg-green-50 text-green-800 font-semibold' : ''}`}>
                        {row.prop2}
                      </td>
                      <td className={`text-center py-3 px-4 ${row.winner === 'prop3' ? 'bg-green-50 text-green-800 font-semibold' : ''}`}>
                        {row.prop3}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Market Comparison */}
      {comparisonType === "markets" && (
        <Card className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-purple-600" />
              Market Region Comparison
            </CardTitle>
            <CardDescription>
              Compare performance across different market regions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              {marketComparison.map((market, index) => (
                <div key={index} className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <MapPin className="h-6 w-6 text-purple-600" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{market.region}</h4>
                        <p className="text-sm text-gray-500">Market Region</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-6">
                      <div className="text-center">
                        <p className="text-sm text-gray-500">Avg Rent</p>
                        <p className="font-semibold text-gray-900">{market.avgRent}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-500">Vacancy</p>
                        <p className="font-semibold text-gray-900">{market.vacancy}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-500">Growth</p>
                        <div className="flex items-center gap-1">
                          <TrendingUp className="h-3 w-3 text-green-500" />
                          <p className="font-semibold text-green-600">{market.growth}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Performance Analysis */}
      {comparisonType === "performance" && (
        <div className="grid gap-6 lg:grid-cols-2">
          <Card className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5 text-green-600" />
                Portfolio Performance
              </CardTitle>
              <CardDescription>
                Overall portfolio performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <PieChart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 text-lg">Performance chart will be displayed here</p>
                  <p className="text-sm text-gray-400">Integration with charting library needed</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                Benchmark Comparison
              </CardTitle>
              <CardDescription>
                Compare against market benchmarks
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium">vs Market Average</span>
                  <Badge className="bg-green-100 text-green-800">+12% Better</Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium">vs Regional Average</span>
                  <Badge className="bg-blue-100 text-blue-800">+8% Better</Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium">vs Property Type</span>
                  <Badge className="bg-purple-100 text-purple-800">+15% Better</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
