"use client"

import { useEffect, useState } from "react"
import { use<PERSON>earchP<PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { Building2, Check, ChevronDown, ChevronLeft, ChevronRight, Loader2, MoreHorizontal, Plus, Search, SlidersHorizontal } from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useToast } from "@/components/ui/use-toast"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenu<PERSON><PERSON>,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { InfoIcon } from "lucide-react"
import Cookies from "js-cookie"
import { propertiesService, PropertyStatus, Property } from "@/lib/services"

// Status badge variant mapping
const getStatusVariant = (status: PropertyStatus) => {
  switch (status) {
    case 'LIVE':
      return 'default'; // Using default variant (primary color) for LIVE properties
    case 'RENTED':
      return 'secondary';
    case 'PAUSED':
      return 'destructive'; // Using destructive variant for PAUSED properties
    default:
      return 'outline';
  }
}

// Status display text mapping
const getStatusText = (status: PropertyStatus) => {
  switch (status) {
    case 'LIVE':
      return 'Live';
    case 'RENTED':
      return 'Rented';
    case 'PAUSED':
      return 'Paused';
    default:
      return status;
  }
}

// Get status icon based on status
const getStatusIcon = (status: PropertyStatus) => {
  switch (status) {
    case 'LIVE':
      return <div className="h-2 w-2 rounded-full bg-green-500 mr-1.5" />;
    case 'RENTED':
      return <div className="h-2 w-2 rounded-full bg-blue-500 mr-1.5" />;
    case 'PAUSED':
      return <div className="h-2 w-2 rounded-full bg-red-500 mr-1.5" />;
    default:
      return null;
  }
}

// Get status color classes
const getStatusColorClasses = (status: PropertyStatus) => {
  switch (status) {
    case 'LIVE':
      return 'bg-green-50 text-green-700 border-green-200';
    case 'RENTED':
      return 'bg-blue-50 text-blue-700 border-blue-200';
    case 'PAUSED':
      return 'bg-red-50 text-red-700 border-red-200';
    default:
      return 'bg-gray-50 text-gray-700 border-gray-200';
  }
}

export default function PropertiesPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { toast } = useToast()
  const [properties, setProperties] = useState<Property[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [updatingStatus, setUpdatingStatus] = useState<{id?: string, loading: boolean}>({loading: false})
  const [statusDialogOpen, setStatusDialogOpen] = useState(false)
  const [pendingStatusChange, setPendingStatusChange] = useState<{propertyId?: string, status?: PropertyStatus, propertyTitle?: string}>({})
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(5)

  // Function to handle status change request
  const handleStatusChange = (propertyId: string | undefined, newStatus: PropertyStatus, propertyTitle: string) => {
    setPendingStatusChange({
      propertyId,
      status: newStatus,
      propertyTitle
    })
    setStatusDialogOpen(true)
  }

  // Function to confirm and execute status change
  const confirmStatusChange = async () => {
    if (!pendingStatusChange.propertyId || !pendingStatusChange.status) {
      setStatusDialogOpen(false)
      return
    }
    
    await updatePropertyStatus(pendingStatusChange.propertyId, pendingStatusChange.status)
    setStatusDialogOpen(false)
  }

  // Function to fetch properties from API
  const fetchProperties = async (showLoading = true) => {
    if (showLoading) {
      setLoading(true);
    }
    setError(null);

    try {
      const response = await propertiesService.getProperties();

      if (response.status === 401 || response.status === 403) {
        // Token expired or invalid
        setError("Your session has expired. Please log in again.");
        router.push('/login');
        return;
      }

      if (response.error) {
        throw new Error(response.error);
      }

      setProperties(response.data);
    } catch (error) {
      console.error('Error fetching properties:', error);
      setError("Failed to load properties. Please try again.");
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  };

  // Function to update property status
  const updatePropertyStatus = async (propertyId: string | undefined, newStatus: PropertyStatus) => {
    if (!propertyId) {
      toast({
        title: "Error",
        description: "Property ID is missing",
        variant: "destructive",
      });
      return;
    }

    setUpdatingStatus({id: propertyId, loading: true});

    try {
      const response = await propertiesService.updatePropertyStatus({
        propertyId,
        status: newStatus
      });

      if (response.status === 401 || response.status === 403) {
        // Token expired or invalid
        toast({
          title: "Error",
          description: "Your session has expired. Please log in again.",
          variant: "destructive",
        });
        router.push('/login');
        return;
      }

      if (response.error) {
        // Error toast is already shown by the API service, just return
        return;
      }

      // Refresh the properties list to get updated data from server
      await fetchProperties(false); // Don't show loading spinner since we have status-specific loading

      toast({
        title: "Success",
        description: `Property status updated to ${getStatusText(newStatus)}`,
      });

      // Close the dialog
      setStatusDialogOpen(false);

    } catch (error) {
      console.error('Error updating property status:', error);
      // Don't show error toast here as it's already handled by the API service
      // Only log the error for debugging purposes
    } finally {
      setUpdatingStatus({loading: false});
    }
  };

  // Fetch properties from API on component mount
  useEffect(() => {
    fetchProperties();
  }, [router, toast]);

  // Show success toast when property is added
  useEffect(() => {
    const success = searchParams.get("success")
    if (success === "true") {
      toast({
        title: "Property Added",
        description: "Your property has been successfully added.",
      })
    }
  }, [searchParams, toast])
  
  // Filter properties based on status and search query
  const filteredProperties = properties.filter(property => {
    // Status filter
    if (statusFilter !== 'all' && property.status !== statusFilter) {
      return false
    }
    
    // Search query filter
    if (searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase()
      return (
        (property.title && property.title.toLowerCase().includes(query)) ||
        (property.address && property.address.toLowerCase().includes(query))
      )
    }
    
    return true
  })
  
  // Calculate pagination
  const totalPages = Math.ceil(filteredProperties.length / itemsPerPage)
  const paginatedProperties = filteredProperties.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )
  
  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50/30">
      {/* Enhanced Header with Gradient Background */}
      <div className="relative overflow-hidden bg-gradient-to-br from-purple-600 via-purple-700 to-pink-600 text-white">
        <div className="absolute inset-0 bg-gradient-to-br from-black/20 to-transparent"></div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 h-24 w-24 rounded-full bg-white/10 blur-2xl"></div>
        <div className="absolute bottom-0 left-0 -mb-8 -ml-8 h-32 w-32 rounded-full bg-white/5 blur-3xl"></div>

        <div className="relative z-10 px-4 sm:px-6 py-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              <div className="flex-1">
                <div className="flex items-center gap-4 mb-4">
                  <div className="p-3 bg-white/20 backdrop-blur-sm rounded-2xl">
                    <Building2 className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-4xl sm:text-5xl font-bold tracking-tight">Properties</h1>
                    <p className="text-purple-100 text-lg mt-2">
                      Manage and monitor your property portfolio
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <Button
                  asChild
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30 backdrop-blur-sm rounded-xl px-6 py-3 font-semibold transition-all duration-200 hover:scale-105"
                >
                  <Link href="/dashboard/properties/add">
                    <Plus className="h-5 w-5 mr-2" />
                    Add Property
                  </Link>
                </Button>
              </div>
            </div>

            {/* Stats Cards in Header */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-8">
              <div className="bg-white/15 backdrop-blur-sm rounded-2xl p-4 border border-white/30">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-white/25 rounded-xl flex items-center justify-center">
                    <Building2 className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className="text-white/80 text-xs font-medium uppercase tracking-wide">Total Properties</p>
                    <p className="text-white text-xl font-bold">{properties.length}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white/15 backdrop-blur-sm rounded-2xl p-4 border border-white/30">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-white/25 rounded-xl flex items-center justify-center">
                    <Check className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className="text-white/80 text-xs font-medium uppercase tracking-wide">Live Properties</p>
                    <p className="text-white text-xl font-bold">
                      {properties.filter(p => p.status === 'LIVE').length}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white/15 backdrop-blur-sm rounded-2xl p-4 border border-white/30">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-white/25 rounded-xl flex items-center justify-center">
                    <Building2 className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className="text-white/80 text-xs font-medium uppercase tracking-wide">Rented Properties</p>
                    <p className="text-white text-xl font-bold">
                      {properties.filter(p => p.status === 'RENTED').length}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Container */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-8 space-y-8">
        {/* Enhanced Info Alert */}
        <Alert className="border-0 bg-gradient-to-r from-blue-50 to-blue-100/50 backdrop-blur-xl shadow-lg rounded-2xl">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-blue-100 rounded-xl">
              <InfoIcon className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <AlertTitle className="text-blue-900 font-bold mb-1">Important Information</AlertTitle>
              <AlertDescription className="text-blue-800 leading-relaxed">
                Once a property is marked as <span className="font-semibold bg-blue-200/50 px-1 rounded">Rented</span>, its status cannot be changed. This ensures accurate record-keeping for rented properties.
              </AlertDescription>
            </div>
          </div>
        </Alert>
      
      {/* Status Change Confirmation Dialog */}
      <AlertDialog open={statusDialogOpen} onOpenChange={setStatusDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Change Property Status</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to change the status of <span className="font-medium">{pendingStatusChange.propertyTitle}</span> to <span className="font-medium">{pendingStatusChange.status && getStatusText(pendingStatusChange.status)}</span>?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmStatusChange}
              className={`${
                pendingStatusChange.status === 'LIVE' 
                  ? 'bg-green-600 hover:bg-green-700' 
                  : pendingStatusChange.status === 'RENTED' 
                    ? 'bg-blue-600 hover:bg-blue-700' 
                    : 'bg-red-600 hover:bg-red-700'
              }`}
            >
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
        {/* Enhanced Properties Table Card */}
        <Card className="border-0 bg-white/70 backdrop-blur-xl shadow-2xl overflow-hidden rounded-3xl">
          <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 border-b border-purple-100/50 p-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-purple-100 rounded-2xl">
                  <Building2 className="h-8 w-8 text-purple-600" />
                </div>
                <div>
                  <CardTitle className="text-3xl font-bold text-gray-900">Property Portfolio</CardTitle>
                  <CardDescription className="text-gray-600 mt-2 text-lg">
                    Manage and monitor all your properties in one place
                  </CardDescription>
                </div>
              </div>
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4 mt-6">
              {/* Search */}
              <div className="relative flex-1 group">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 group-focus-within:text-purple-500 transition-colors duration-200" />
                <Input
                  type="search"
                  placeholder="Search by property name or address..."
                  className="pl-10 pr-3 py-2 bg-white border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500/20 focus:border-purple-400 transition-all duration-200 shadow-sm"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              {/* Filters */}
              <div className="flex items-center">
                <div className="flex items-center gap-2 bg-gray-50 rounded-xl px-3 py-2 border border-gray-200">
                  <SlidersHorizontal className="h-4 w-4 text-gray-600" />
                  <Select
                    value={statusFilter}
                    onValueChange={setStatusFilter}
                  >
                    <SelectTrigger className="w-[140px] bg-white border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500/20 focus:border-purple-400 transition-all duration-200 h-8">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent className="rounded-lg border-gray-200">
                      <SelectItem value="all">All Properties</SelectItem>
                      <SelectItem value="LIVE">Live Properties</SelectItem>
                      <SelectItem value="RENTED">Rented Properties</SelectItem>
                      <SelectItem value="PAUSED">Paused Properties</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </CardHeader>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex flex-col justify-center items-center py-20 gap-6">
              <div className="relative">
                <div className="h-20 w-20 rounded-full bg-gradient-to-br from-purple-100 to-purple-200 flex items-center justify-center animate-pulse">
                  <Building2 className="h-10 w-10 text-purple-600" />
                </div>
                <div className="absolute inset-0 rounded-full border-4 border-purple-600 border-t-transparent animate-spin"></div>
              </div>
              <div className="text-center">
                <h3 className="text-xl font-bold text-gray-900 mb-2">Loading Properties</h3>
                <p className="text-gray-600">Fetching your property portfolio...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center py-20 text-center">
              <div className="relative mb-6">
                <div className="h-20 w-20 rounded-full bg-gradient-to-br from-red-100 to-red-200 flex items-center justify-center">
                  <Building2 className="h-10 w-10 text-red-600" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-3">Error Loading Properties</h3>
              <p className="text-gray-600 mb-6 max-w-md leading-relaxed">{error}</p>
              <Button
                onClick={() => fetchProperties()}
                className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white rounded-xl px-6 py-3 shadow-lg hover:shadow-xl transition-all duration-200"
              >
                Try Again
              </Button>
            </div>
          ) : (
            <div className="relative">
              <ScrollArea className="h-[600px] w-full">
                <div className="w-full min-w-full">
                  <Table>
                    <TableHeader>
                      <TableRow className="sticky top-0 bg-gradient-to-r from-purple-50/95 to-pink-50/95 backdrop-blur-sm z-20 border-b border-purple-200/50 shadow-sm">
                        <TableHead className="font-bold text-gray-900 bg-gradient-to-r from-purple-50/95 to-pink-50/95 backdrop-blur-sm py-3 text-sm">Property Details</TableHead>
                        <TableHead className="hidden md:table-cell font-bold text-gray-900 bg-gradient-to-r from-purple-50/95 to-pink-50/95 backdrop-blur-sm py-3 text-sm">Monthly Rent</TableHead>
                        <TableHead className="text-right font-bold text-gray-900 bg-gradient-to-r from-purple-50/95 to-pink-50/95 backdrop-blur-sm py-3 text-sm">Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                  {paginatedProperties.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-20">
                        <div className="flex flex-col items-center justify-center gap-6">
                          <div className="relative">
                            <div className="h-20 w-20 rounded-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                              <Building2 className="h-10 w-10 text-gray-400" />
                            </div>
                            <div className="absolute -top-2 -right-2 h-8 w-8 bg-gray-400 rounded-full flex items-center justify-center">
                              <Search className="h-4 w-4 text-white" />
                            </div>
                          </div>
                          <div className="text-center">
                            <h3 className="text-2xl font-bold text-gray-900 mb-3">No Properties Found</h3>
                            <p className="text-gray-600 max-w-md leading-relaxed mb-6">
                              {searchQuery || statusFilter !== 'all'
                                ? "No properties match your current search criteria. Try adjusting your filters or search terms."
                                : "You haven't added any properties yet. Start by adding your first property to get started."
                              }
                            </p>
                            {searchQuery || statusFilter !== 'all' ? (
                              <Button
                                variant="outline"
                                onClick={() => {
                                  setSearchQuery('')
                                  setStatusFilter('all')
                                }}
                                className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
                              >
                                Clear Filters
                              </Button>
                            ) : (
                              <Button
                                asChild
                                className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white rounded-xl px-6 py-3 shadow-lg hover:shadow-xl transition-all duration-200"
                              >
                                <Link href="/dashboard/properties/add">
                                  <Plus className="h-4 w-4 mr-2" />
                                  Add Your First Property
                                </Link>
                              </Button>
                            )}
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    paginatedProperties.map((property, index) => (
                      <TableRow
                        key={property.id || index}
                        className="group cursor-pointer hover:bg-gradient-to-r hover:from-purple-50/50 hover:to-pink-50/50 border-b border-gray-100/60 transition-all duration-200 hover:shadow-sm"
                        onClick={() => router.push(`/dashboard/properties/${property.id}`)}
                      >
                        <TableCell className="py-3">
                          <div className="flex items-center gap-4">
                            <div className="relative">
                              <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-purple-100 to-purple-200 shadow-sm flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
                                <Building2 className="h-6 w-6 text-purple-600" />
                              </div>
                              <div className={`absolute -top-0.5 -right-0.5 w-3 h-3 rounded-full ${
                                property.status === 'LIVE' ? 'bg-green-500' :
                                property.status === 'RENTED' ? 'bg-blue-500' : 'bg-red-500'
                              } border-2 border-white`}></div>
                            </div>
                            <div className="flex-1">
                              <div className="font-semibold text-gray-900 group-hover:text-purple-600 transition-colors duration-200 mb-0.5">
                                {property.title}
                              </div>
                              <div className="text-sm text-gray-600">{property.address}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell py-3">
                          <div className="text-right">
                            <div className="text-lg font-bold text-gray-900">
                              {property.price ? new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: 'USD',
                                maximumFractionDigits: 0
                              }).format(property.price) : 'N/A'}
                            </div>
                            <div className="text-xs text-gray-500">per month</div>
                          </div>
                        </TableCell>
                        <TableCell className="text-right pr-4 py-3">
                          <div className="flex items-center justify-end gap-2">
                            {property.status === 'RENTED' ? (
                              <div className={`flex items-center px-3 py-1.5 rounded-xl text-xs font-semibold border ${getStatusColorClasses(property.status)} shadow-sm`}>
                                {getStatusIcon(property.status)}
                                {getStatusText(property.status)}
                              </div>
                            ) : (
                              <div className="flex items-center rounded-xl overflow-hidden shadow-sm border border-gray-200">
                                <button
                                  className={`flex items-center px-3 py-1.5 text-xs font-semibold transition-all duration-200 ${getStatusColorClasses(property.status)}`}
                                  disabled={updatingStatus.loading}
                                >
                                  {getStatusIcon(property.status)}
                                  {getStatusText(property.status)}
                                </button>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <button
                                      className={`px-2 py-1.5 border-l transition-all duration-200 ${
                                        property.status === 'LIVE'
                                          ? 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100'
                                          : property.status === 'PAUSED'
                                            ? 'bg-red-50 border-red-200 text-red-700 hover:bg-red-100'
                                            : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                                      }`}
                                      disabled={updatingStatus.loading}
                                      onClick={(e) => e.stopPropagation()}
                                    >
                                      {updatingStatus.id === property.id && updatingStatus.loading ? (
                                        <Loader2 className="h-3 w-3 animate-spin" />
                                      ) : (
                                        <ChevronDown className="h-3 w-3" />
                                      )}
                                    </button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end" className="w-48 rounded-xl border-purple-200/60 shadow-xl">
                                    <DropdownMenuLabel className="text-xs font-semibold text-purple-600 uppercase tracking-wide px-2 py-1.5">Change Status</DropdownMenuLabel>
                                    <DropdownMenuSeparator className="bg-purple-100" />
                                    {property.status !== 'LIVE' && (
                                      <DropdownMenuItem
                                        className="text-xs flex items-center gap-2 p-2 rounded-lg hover:bg-green-50 focus:bg-green-50 text-green-700 font-medium transition-all duration-200 mx-1"
                                        disabled={updatingStatus.loading}
                                        onClick={(e) => {
                                          e.stopPropagation()
                                          handleStatusChange(property.id, 'LIVE', property.title)
                                        }}
                                      >
                                        <div className="p-1 bg-green-100 rounded">
                                          <div className="h-2 w-2 rounded-full bg-green-500" />
                                        </div>
                                        Make Live
                                      </DropdownMenuItem>
                                    )}
                                    {property.status !== 'RENTED' && (
                                      <DropdownMenuItem
                                        className="text-xs flex items-center gap-2 p-2 rounded-lg hover:bg-blue-50 focus:bg-blue-50 text-blue-700 font-medium transition-all duration-200 mx-1"
                                        disabled={updatingStatus.loading}
                                        onClick={(e) => {
                                          e.stopPropagation()
                                          handleStatusChange(property.id, 'RENTED', property.title)
                                        }}
                                      >
                                        <div className="p-1 bg-blue-100 rounded">
                                          <div className="h-2 w-2 rounded-full bg-blue-500" />
                                        </div>
                                        Mark as Rented
                                      </DropdownMenuItem>
                                    )}
                                    {property.status !== 'PAUSED' && (
                                      <DropdownMenuItem
                                        className="text-xs flex items-center gap-2 p-2 rounded-lg hover:bg-red-50 focus:bg-red-50 text-red-700 font-medium transition-all duration-200 mx-1"
                                        disabled={updatingStatus.loading}
                                        onClick={(e) => {
                                          e.stopPropagation()
                                          handleStatusChange(property.id, 'PAUSED', property.title)
                                        }}
                                      >
                                        <div className="p-1 bg-red-100 rounded">
                                          <div className="h-2 w-2 rounded-full bg-red-500" />
                                        </div>
                                        Pause Property
                                      </DropdownMenuItem>
                                    )}
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                    </TableBody>
                  </Table>
                </div>
              </ScrollArea>
            </div>
          )}
          
          {/* Enhanced Pagination */}
          {filteredProperties.length > 0 && (
            <CardFooter className="flex flex-col lg:flex-row items-center justify-between bg-gradient-to-r from-purple-50/50 to-pink-50/50 border-t border-purple-100/50 px-6 py-4 gap-4">
              <div className="text-sm text-gray-600 order-2 lg:order-1 font-medium">
                Showing <span className="font-bold text-purple-600">{Math.min((currentPage - 1) * itemsPerPage + 1, filteredProperties.length)}</span> to{" "}
                <span className="font-bold text-purple-600">{Math.min(currentPage * itemsPerPage, filteredProperties.length)}</span> of{" "}
                <span className="font-bold text-purple-600">{filteredProperties.length}</span> properties
              </div>
              <div className="flex items-center gap-4 order-1 lg:order-2">
                <div className="flex items-center gap-2 bg-white/80 rounded-xl px-3 py-1.5 border border-purple-200/60">
                  <span className="text-sm text-gray-600 font-medium">Show</span>
                  <Select
                    value={itemsPerPage.toString()}
                    onValueChange={(value) => {
                      setItemsPerPage(Number(value))
                      setCurrentPage(1) // Reset to first page when changing items per page
                    }}
                  >
                    <SelectTrigger className="h-7 w-16 bg-white border-purple-200/60 rounded-lg text-sm">
                      <SelectValue placeholder="5" />
                    </SelectTrigger>
                    <SelectContent className="rounded-lg border-purple-200/60">
                      <SelectItem value="5">5</SelectItem>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                    </SelectContent>
                  </Select>
                  <span className="text-sm text-gray-600 font-medium">per page</span>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8 rounded-xl bg-white/80 border-purple-200/60 hover:bg-purple-50 hover:border-purple-300 transition-all duration-200"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-3 w-3" />
                  </Button>
                  {Array.from({ length: totalPages }, (_, i) => i + 1)
                    .filter(page => {
                      // Show first page, last page, current page, and pages around current page
                      return page === 1 ||
                             page === totalPages ||
                             Math.abs(page - currentPage) <= 1
                    })
                    .map((page, index, array) => {
                      // Add ellipsis if there are gaps
                      const showEllipsisBefore = index > 0 && page - array[index - 1] > 1

                      return (
                        <div key={page} className="flex items-center">
                          {showEllipsisBefore && (
                            <span className="px-3 text-purple-400 font-bold">...</span>
                          )}
                          <Button
                            variant={currentPage === page ? "default" : "outline"}
                            size="icon"
                            className={`h-8 w-8 rounded-xl font-semibold transition-all duration-200 text-sm ${
                              currentPage === page
                                ? 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg'
                                : 'bg-white/80 border-purple-200/60 hover:bg-purple-50 hover:border-purple-300 text-purple-600'
                            }`}
                            onClick={() => handlePageChange(page)}
                          >
                            {page}
                          </Button>
                        </div>
                      )
                    })}
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8 rounded-xl bg-white/80 border-purple-200/60 hover:bg-purple-50 hover:border-purple-300 transition-all duration-200"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRight className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </CardFooter>
          )}
        </CardContent>
      </Card>
      </div>
    </div>
  )
}