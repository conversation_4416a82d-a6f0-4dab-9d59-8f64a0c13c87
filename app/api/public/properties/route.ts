import { NextRequest, NextResponse } from 'next/server'

// Mock data for development - replace with actual database queries
const mockProperties = [
  {
    id: "1",
    title: "Modern Downtown Apartment",
    address: "123 Rue Saint-Catherine, Montreal, QC H3B 1A7",
    price: 1800,
    bedrooms: 2,
    bathrooms: 1,
    parking: 1,
    area: 850,
    images: ["/api/placeholder/800/600", "/api/placeholder/800/600"],
    type: "Apartment",
    status: "LIVE",
    description: "Beautiful modern apartment in the heart of downtown Montreal with stunning city views.",
    amenities: ["Hardwood floors", "Stainless steel appliances", "In-unit laundry", "Air conditioning", "Balcony", "Gym access"],
    features: ["elevator", "pets", "furnished"],
    landlord: {
      name: "Property Management Inc.",
      phone: "(*************",
      email: "<EMAIL>"
    },
    availableDate: "2024-02-01",
    coordinates: { lat: 45.5017, lng: -73.5673 }
  },
  {
    id: "2",
    title: "Cozy Studio in Plateau",
    address: "456 Avenue Mont-Royal, Montreal, QC H2T 1V6",
    price: 1200,
    bedrooms: 1,
    bathrooms: 1,
    parking: 0,
    area: 450,
    images: ["/api/placeholder/800/600", "/api/placeholder/800/600"],
    type: "Studio",
    status: "LIVE",
    description: "Charming studio apartment in trendy Plateau neighborhood with exposed brick walls.",
    amenities: ["Exposed brick", "High ceilings", "Natural light", "Close to metro"],
    features: ["pets", "smoking"],
    landlord: {
      name: "Plateau Properties",
      phone: "(*************",
      email: "<EMAIL>"
    },
    availableDate: "2024-01-15",
    coordinates: { lat: 45.5276, lng: -73.5785 }
  },
  {
    id: "3",
    title: "Spacious Family Home",
    address: "789 Rue de la Paix, Quebec City, QC G1R 2L5",
    price: 2500,
    bedrooms: 3,
    bathrooms: 2,
    parking: 2,
    area: 1200,
    images: ["/api/placeholder/800/600", "/api/placeholder/800/600"],
    type: "House",
    status: "LIVE",
    description: "Perfect family home with garden and garage in quiet neighborhood.",
    amenities: ["Private garden", "Garage", "Fireplace", "Updated kitchen", "Hardwood floors"],
    features: ["pool", "mobility", "semi-furnished"],
    landlord: {
      name: "Quebec Family Homes",
      phone: "(*************",
      email: "<EMAIL>"
    },
    availableDate: "2024-03-01",
    coordinates: { lat: 46.8139, lng: -71.2080 }
  }
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Extract query parameters
    const query = searchParams.get('query')
    const priceMin = searchParams.get('priceMin')
    const priceMax = searchParams.get('priceMax')
    const bedrooms = searchParams.get('bedrooms')
    const bathrooms = searchParams.get('bathrooms')
    const propertyType = searchParams.get('propertyType')
    const location = searchParams.get('location')
    const features = searchParams.get('features')
    const category = searchParams.get('category')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    // Filter properties based on query parameters
    let filteredProperties = mockProperties

    if (query) {
      const queryLower = query.toLowerCase()
      filteredProperties = filteredProperties.filter(p => 
        p.title.toLowerCase().includes(queryLower) || 
        p.address.toLowerCase().includes(queryLower)
      )
    }

    if (priceMin) {
      filteredProperties = filteredProperties.filter(p => p.price >= parseInt(priceMin))
    }

    if (priceMax) {
      filteredProperties = filteredProperties.filter(p => p.price <= parseInt(priceMax))
    }

    if (bedrooms) {
      filteredProperties = filteredProperties.filter(p => p.bedrooms >= parseInt(bedrooms))
    }

    if (bathrooms) {
      filteredProperties = filteredProperties.filter(p => p.bathrooms >= parseInt(bathrooms))
    }

    if (propertyType && propertyType !== 'all') {
      filteredProperties = filteredProperties.filter(p => 
        p.type.toLowerCase() === propertyType.toLowerCase()
      )
    }

    if (location) {
      const locationLower = location.toLowerCase()
      filteredProperties = filteredProperties.filter(p =>
        p.address.toLowerCase().includes(locationLower)
      )
    }

    if (features) {
      const featureList = features.split(',')
      filteredProperties = filteredProperties.filter(p =>
        featureList.every(feature => p.features.includes(feature))
      )
    }

    if (category && category !== 'all') {
      // For now, all mock properties are residential
      // In a real app, you would filter based on property category
      if (category === 'commercial') {
        filteredProperties = []
      }
    }

    // Pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedProperties = filteredProperties.slice(startIndex, endIndex)

    const response = {
      properties: paginatedProperties,
      total: filteredProperties.length,
      page,
      totalPages: Math.ceil(filteredProperties.length / limit)
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error in public properties API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
