import { NextRequest, NextResponse } from 'next/server'

// Mock data for development - replace with actual database queries
const mockProperties = [
  {
    id: "1",
    title: "Modern Downtown Apartment",
    address: "123 Rue Saint-Catherine, Montreal, QC H3B 1A7",
    price: 1800,
    bedrooms: 2,
    bathrooms: 1,
    parking: 1,
    area: 850,
    images: [
      "/api/placeholder/800/600",
      "/api/placeholder/800/600", 
      "/api/placeholder/800/600",
      "/api/placeholder/800/600"
    ],
    type: "Apartment",
    status: "LIVE",
    description: "Beautiful modern apartment in the heart of downtown Montreal. This stunning 2-bedroom, 1-bathroom unit features floor-to-ceiling windows, hardwood floors, and a modern kitchen with stainless steel appliances. Located in a prime location with easy access to public transportation, shopping, and dining.",
    amenities: [
      "Hardwood floors",
      "Stainless steel appliances", 
      "In-unit laundry",
      "Air conditioning",
      "Balcony",
      "Gym access",
      "Rooftop terrace",
      "24/7 security"
    ],
    landlord: {
      name: "Property Management Inc.",
      phone: "(*************",
      email: "<EMAIL>"
    },
    availableDate: "2024-02-01",
    coordinates: { lat: 45.5017, lng: -73.5673 }
  },
  {
    id: "2",
    title: "Cozy Studio in Plateau",
    address: "456 Avenue Mont-Royal, Montreal, QC H2T 1V6",
    price: 1200,
    bedrooms: 1,
    bathrooms: 1,
    parking: 0,
    area: 450,
    images: [
      "/api/placeholder/800/600",
      "/api/placeholder/800/600"
    ],
    type: "Studio",
    status: "LIVE",
    description: "Charming studio apartment in trendy Plateau neighborhood with exposed brick walls and high ceilings. Perfect for young professionals or students.",
    amenities: [
      "Exposed brick",
      "High ceilings",
      "Natural light",
      "Close to metro",
      "Hardwood floors"
    ],
    landlord: {
      name: "Plateau Properties",
      phone: "(*************",
      email: "<EMAIL>"
    },
    availableDate: "2024-01-15",
    coordinates: { lat: 45.5276, lng: -73.5785 }
  },
  {
    id: "3",
    title: "Spacious Family Home",
    address: "789 Rue de la Paix, Quebec City, QC G1R 2L5",
    price: 2500,
    bedrooms: 3,
    bathrooms: 2,
    parking: 2,
    area: 1200,
    images: [
      "/api/placeholder/800/600",
      "/api/placeholder/800/600",
      "/api/placeholder/800/600"
    ],
    type: "House",
    status: "LIVE",
    description: "Perfect family home with garden and garage in quiet neighborhood. Recently renovated with modern amenities while maintaining classic charm.",
    amenities: [
      "Private garden",
      "Garage",
      "Fireplace",
      "Updated kitchen",
      "Hardwood floors",
      "Central air",
      "Basement storage"
    ],
    landlord: {
      name: "Quebec Family Homes",
      phone: "(*************",
      email: "<EMAIL>"
    },
    availableDate: "2024-03-01",
    coordinates: { lat: 46.8139, lng: -71.2080 }
  }
]

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // Find property by ID
    const property = mockProperties.find(p => p.id === id)

    if (!property) {
      return NextResponse.json(
        { error: 'Property not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(property)
  } catch (error) {
    console.error('Error in public property details API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
