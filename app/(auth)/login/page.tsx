"use client";

import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { Loader2 } from "lucide-react";
import { getAuthUrl } from "@/lib/auth.client";

export default function LoginPage() {
  const searchParams = useSearchParams();
  
  useEffect(() => {
    // Redirect to the Cognito hosted UI
    const redirectToAuth = async () => {
      try {
        // Get the redirect parameter if any
        const redirectPath = searchParams.get('redirect');
        
        // Get the auth URL for implicit flow
        const authUrl = await getAuthUrl(true);
        
        // Log the URL for debugging
        console.log("Redirecting to Cognito:", authUrl);
        
        // Redirect to the auth URL
        window.location.href = authUrl;
      } catch (error) {
        console.error("Error generating auth URL:", error);
      }
    };
    
    redirectToAuth();
  }, [searchParams]);
  
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
        <h1 className="text-xl font-semibold">Redirecting to login...</h1>
        <p className="text-sm text-gray-500 mt-2">You will be redirected to the authentication page.</p>
      </div>
    </div>
  );
}
