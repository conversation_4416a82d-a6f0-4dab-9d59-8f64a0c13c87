"use client";

import { useEffect } from "react";
import { Loader2 } from "lucide-react";
import { COGNITO_CONFIG } from "@/lib/auth-config";

export default function LogoutPage() {
  useEffect(() => {
    const logout = async () => {
      try {
        // Clear local session
        await fetch("/api/auth/logout", { method: "POST" });
        
        // Get the redirect URI
        const redirectUri = window.location.origin;
        
        // Build the Cognito logout URL
        const logoutUrl = `https://${COGNITO_CONFIG.DOMAIN}/logout?client_id=${COGNITO_CONFIG.CLIENT_ID}&logout_uri=${redirectUri}`;
        
        // Redirect to Cognito logout
        window.location.href = logoutUrl;
      } catch (error) {
        console.error("Error during logout:", error);
        // Fallback to home page
        window.location.href = "/";
      }
    };
    
    logout();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
        <h1 className="text-xl font-semibold">Signing out...</h1>
        <p className="text-sm text-gray-500 mt-2">You will be redirected shortly.</p>
      </div>
    </div>
  );
}