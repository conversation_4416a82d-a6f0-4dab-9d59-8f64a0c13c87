"use client"

import { useState, use<PERSON><PERSON>back, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { useDropzone } from "react-dropzone"
import Image from "next/image"
import Cookies from "js-cookie"
import {
  ArrowLeft,
  Bath,
  BedDouble,
  Building,
  Building2,
  CalendarIcon,
  DollarSign,
  FileText,
  GripVertical,
  Home,
  ImageIcon,
  Info,
  Loader2,
  MapPin,
  Mail,
  MoveHorizontal,
  Trash2,
  Upload,
  X,
} from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { cn } from "@/lib/utils"

import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Checkbox } from "@/components/ui/checkbox"
import { format } from "date-fns"
import { Calendar } from "@/components/ui/calendar"
import { useToast } from "@/components/ui/use-toast"
// Import Mapbox GL CSS
import 'mapbox-gl/dist/mapbox-gl.css'
import env from "@/lib/env"
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import { propertySchema } from "@/lib/validations";
import type { PropertyFormValues } from "@/lib/types";

// Use Mapbox token from environment configuration
const MAPBOX_TOKEN = env.MAPBOX.TOKEN

type AddressFeature = {
  place_name: string
  context: Array<{
    id: string
    text: string
    short_code?: string
  }>
  text: string
}

type AddressSearchResult = {
  features: AddressFeature[]
  type: string
}

// Schema and types are now imported from @/validations and @/types

export default function AddPropertyPage() {
  const router = useRouter()
  // Form steps are now using fixed active states
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const [addressQuery, setAddressQuery] = useState("")
  const [addressSuggestions, setAddressSuggestions] = useState<AddressFeature[]>([])
  const [isLoadingAddress, setIsLoadingAddress] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [addressError, setAddressError] = useState("")
  const [open, setOpen] = useState(false)
  const [selectedLocation, setSelectedLocation] = useState<AddressFeature | null>(null)
  const [userLocation, setUserLocation] = useState<{lat: number, lng: number} | null>(null)
  const [mapInitialized, setMapInitialized] = useState(false)
  const mapRef = useRef<any>(null)
  const mapContainerRef = useRef<HTMLDivElement | null>(null)
  const { toast } = useToast()
  
  // State for verification checkboxes
  const [verifications, setVerifications] = useState({
    id_verification: true,
    credit_score: true,
    background_check: false,
    income_verification: false,
    rental_history: false
  })
  
  // Handle verification checkbox changes
  const handleVerificationChange = (id: string, checked: boolean) => {
    setVerifications(prev => ({
      ...prev,
      [id]: checked
    }))
  }

  const form = useForm<PropertyFormValues>({
    resolver: zodResolver(propertySchema),
    defaultValues: {
      title: "",
      type: "",
      description: "",
      address: "",
      city: "",
      province: "",
      zip: "",
      unitNumber: "",
      bedrooms: "",
      bathrooms: "",
      price: "",
      isFreeNow: false,
      startDate: undefined,
      isPetFriendly: false,
      isSmokingAllowed: false,
      isFurnished: false,
      isParkingAvailable: false,
      isSemiFurnished: false,
      requireIdVerification: false,
      requireCreditScore: false,
      requireTAL: false,
      requireBackground: false,
      requireReference: false,
    }
  })

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const filesWithPreview = acceptedFiles.map(file => 
      Object.assign(file, {
        preview: URL.createObjectURL(file)
      })
    );
    setUploadedFiles(prev => [...prev, ...filesWithPreview]);
  }, []);

  const removeFile = (index: number) => {
    setUploadedFiles(prev => {
      const newFiles = [...prev];
      // Revoke the object URL to avoid memory leaks
      if (newFiles[index].preview) {
        URL.revokeObjectURL(newFiles[index].preview);
      }
      newFiles.splice(index, 1);
      return newFiles;
    });
  };

  // Clean up object URLs when component unmounts
  useEffect(() => {
    return () => {
      uploadedFiles.forEach(file => {
        if (file.preview) {
          URL.revokeObjectURL(file.preview);
        }
      });
    };
  }, [uploadedFiles]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".jpeg", ".jpg", ".png"],
    },
    maxFiles: 5,
  })

  const onSubmit = async (data: PropertyFormValues) => {
    try {
      setIsSubmitting(true);
      
      if (!selectedLocation) {
        toast({
          title: "Error",
          description: "Please select a location for your property",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }
      
      // Debug the selectedLocation object
      console.log("Selected Location:", JSON.stringify(selectedLocation, null, 2));
      
      // Create FormData to handle file uploads
      const formData = new FormData();
      
      // Extract coordinates from the selected location
      let latitude = "0";
      let longitude = "0";
      
      if (selectedLocation.center && Array.isArray(selectedLocation.center) && selectedLocation.center.length >= 2) {
        // Mapbox returns coordinates as [longitude, latitude]
        longitude = selectedLocation.center[0].toString();
        latitude = selectedLocation.center[1].toString();
      }
      
      // Extract address components from the selected location
      let addressComponents = {
        city: "",
        province: "",
        zip: ""
      };
      
      if (selectedLocation.place_name) {
        const parts = selectedLocation.place_name.split(", ");
        // Typically, the format is: Address, City, Province Postal Code, Country
        if (parts.length >= 3) {
          addressComponents.city = parts[1] || "";
          
          // The province and postal code are usually in the same part
          const provincePostal = parts[2].split(" ");
          if (provincePostal.length >= 2) {
            addressComponents.province = provincePostal[0] || "";
            addressComponents.zip = provincePostal.slice(1).join(" ") || "";
          }
        }
      }
      
      // Build required verifications string
      const selectedVerifications = Object.entries(verifications)
        .filter(([_, isSelected]) => isSelected)
        .map(([key, _]) => {
          // Convert snake_case to UPPER_CASE
          return key.toUpperCase();
        })
        .join('@');
      
      console.log('Selected verifications:', selectedVerifications);
      
      // Add property data as JSON string
      const propertyData = {
        title: data.title,
        description: data.description,
        address: selectedLocation.place_name ? selectedLocation.place_name.split(", ")[0] || "" : "",
        city: data.city || addressComponents.city,
        province: data.province || addressComponents.province,
        zip: data.zip || addressComponents.zip,
        unitNumber: data.unitNumber || "",
        type: data.type.toUpperCase(),
        price: data.price,
        attitude: parseFloat(latitude) || 0, // Changed from latitude to attitude as per API
        longitude: parseFloat(longitude) || 0,
        bedroomNumber: Number(data.bedrooms) || 0, // Changed from bedrooms to bedroomNumber
        bathroomNumber: Number(data.bathrooms) || 0, // Changed from bathrooms to bathroomNumber
        isPetFriendly: data.isPetFriendly,
        isSmokingAllowed: data.isSmokingAllowed,
        isFreeNow: data.isFreeNow,
        startDate: !data.isFreeNow && data.startDate ? data.startDate.toISOString() : null,
        isFurnished: data.isFurnished,
        isParkingAvailable: data.isParkingAvailable || false,
        isSemiFurnished: data.isSemiFurnished,
        requiredVerifications: selectedVerifications
      };
      
      console.log("Sending property data:", propertyData);
      console.log("API URL:", `${env.API_URL}/properties`);
      
      // Log the FormData contents for debugging
      for (const pair of formData.entries()) {
        if (pair[1] instanceof Blob) {
          console.log(`FormData: ${pair[0]}: [Blob]`);
        } else {
          console.log(`FormData: ${pair[0]}: ${pair[1]}`);
        }
      }
      
      // Get the ID token from cookies (preferred for authentication with user identity)
      const idToken = Cookies.get('id_token');
      // Fallback to access token if ID token is not available
      const accessToken = Cookies.get('session');
      const token = idToken || accessToken;
      
      if (!token) {
        console.error('No authentication token found');
        toast({
          title: "Authentication Error",
          description: "You are not logged in. Please log in and try again.",
          variant: "destructive",
        });
        
        // Redirect to login page
        window.location.href = '/login?redirect=/dashboard/properties/add';
        return;
      }
      
      console.log(`Using ${idToken ? 'ID token' : 'access token'} for API request`);
      
      // Add the property data as JSON string
      formData.append('propertyDto', JSON.stringify(propertyData));
      
      // Add images if available
      if (uploadedFiles.length > 0) {
        uploadedFiles.forEach((file) => {
          formData.append('images', file);
        });
        console.log(`Added ${uploadedFiles.length} images to FormData`);
      } else {
        console.log('No images to upload');
      }
      
      // Log the FormData entries
      console.log('FormData entries:');
      for (const pair of formData.entries()) {
        if (pair[1] instanceof File) {
          console.log(`${pair[0]}: File - ${pair[1].name} (${pair[1].type}, ${pair[1].size} bytes)`);
        } else {
          console.log(`${pair[0]}: ${pair[1]}`);
        }
      }
      
      try {
        // Send the request using fetch with the correct Content-Type
        const response = await fetch(`${env.API_URL}/properties`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
            // Don't set Content-Type - browser will set it with boundary
          },
          body: formData
        });

        console.log("API Response Status:", response.status);

        // Handle the response - explicitly check for status code 200 or 201
        if (response.status === 200 || response.status === 201) {
          console.log("API Success - Property added successfully");

          // Show success toast
          toast({
            title: "Success",
            description: "Property added successfully!",
          });

          // Reset form
          form.reset();
          setUploadedFiles([]);
          setSelectedLocation(null);

          // Redirect to properties list
          router.push('/dashboard/properties');
          return; // Exit early to prevent further processing
        } else {
          // Handle error responses
          let errorMessage = "Failed to add property. Please try again.";

          try {
            const responseText = await response.text();
            console.log("API Error Response:", responseText);

            // Try to parse error message from response
            if (responseText) {
              try {
                const responseData = JSON.parse(responseText);
                if (responseData.error) {
                  errorMessage = responseData.error;
                }
              } catch (e) {
                // If not JSON, use the text as error message if it's reasonable
                if (responseText.length < 200) {
                  errorMessage = responseText;
                }
              }
            }
          } catch (e) {
            console.log("Could not read error response");
          }

          // Check for authentication errors
          if (response.status === 401) {
            toast({
              title: "Authentication Error",
              description: "Your session has expired. Please log in again.",
              variant: "destructive",
            });

            // Redirect to login page
            window.location.href = '/login';
            return;
          }

          // Show error toast
          toast({
            title: "Error",
            description: errorMessage,
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Request failed:", error);
        toast({
          title: "Error",
          description: "Network error occurred while adding the property. Please check your connection and try again.",
          variant: "destructive",
        });
      } finally {
        setIsSubmitting(false);
      }

    } catch (error) {
      console.error('Error submitting property:', error);
      toast({
        title: "Error",
        description: `Failed to add property: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
      setIsSubmitting(false);
    }
  }

  const searchAddress = useCallback(
    async (query: string) => {
      if (!query) {
        setAddressSuggestions([])
        setAddressError("")
        return
      }

      setIsLoadingAddress(true)
      setAddressError("")
      try {
        if (!MAPBOX_TOKEN) {
          throw new Error('Mapbox token is missing')
        }
        
        console.log("Searching for address:", query)
        const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
          query
        )}.json?access_token=${MAPBOX_TOKEN}&country=ca&types=address`
        
        console.log("Fetching from URL:", url.replace(MAPBOX_TOKEN, 'REDACTED'))
        
        const response = await fetch(url)
        
        if (!response.ok) {
          console.error("Mapbox API error:", response.status, response.statusText)
          throw new Error(`Failed to fetch address suggestions: ${response.status} ${response.statusText}`)
        }
        
        const data: AddressSearchResult = await response.json()
        console.log("Received suggestions:", data.features.length)
        
        setAddressSuggestions(data.features)
        if (data.features.length === 0) {
          setAddressError("No addresses found")
        }
      } catch (error) {
        console.error("Error fetching address suggestions:", error)
        setAddressError("Failed to load address suggestions")
        setAddressSuggestions([])
      } finally {
        setIsLoadingAddress(false)
      }
    },
    [MAPBOX_TOKEN]
  )

  const handleAddressSelect = useCallback((feature: AddressFeature) => {
    // Get the full address
    const fullAddress = feature.place_name;
    
    // Set the form values
    form.setValue("address", fullAddress);
    
    // Extract and set city, province, and zip code
    if (feature.place_name) {
      const parts = feature.place_name.split(", ");
      // Typically, the format is: Address, City, Province Postal Code, Country
      if (parts.length >= 3) {
        // Set city
        const city = parts[1] || "";
        form.setValue("city", city);
        
        // The province and postal code are usually in the same part
        const provincePostal = parts[2].split(" ");
        if (provincePostal.length >= 2) {
          const province = provincePostal[0] || "";
          form.setValue("province", province);
          
          const zip = provincePostal.slice(1).join(" ") || "";
          form.setValue("zip", zip);
        }
      }
    }
    
    // Set the selected location to the full feature object
    setSelectedLocation(feature);
    
    setAddressQuery(fullAddress);
    setAddressSuggestions([]);
    setOpen(false);
  }, [form]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (addressQuery.trim() === "") {
        setAddressSuggestions([])
        return
      }

      // Don't search if the query matches the currently selected address
      if (form.getValues("address") === addressQuery) {
        return;
      }

      console.log("Debounced search for:", addressQuery);
      searchAddress(addressQuery);
    }, 500)

    return () => clearTimeout(timer)
  }, [addressQuery, searchAddress, form])

  // Get user's current location when component mounts
  useEffect(() => {
    if (navigator.geolocation && !userLocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
        },
        (error) => {
          console.error("Error getting user location:", error);
          // Default to Montreal if geolocation fails
          setUserLocation({
            lat: 45.5019,
            lng: -73.5674
          });
        },
        { enableHighAccuracy: true, timeout: 5000, maximumAge: 0 }
      );
    } else if (!userLocation) {
      // Default to Montreal if geolocation is not available
      setUserLocation({
        lat: 45.5019,
        lng: -73.5674
      });
    }
  }, [userLocation]);

  // Initialize or update map when location changes
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    // Check if Mapbox token is valid
    if (!MAPBOX_TOKEN) {
      console.error('Mapbox token is missing');
      setMapInitialized(true); // Set to true to hide loading indicator
      return;
    }
    
    // Use selected location if available, otherwise use user location
    let locationToUse;
    
    if (selectedLocation && selectedLocation.center) {
      // Mapbox feature format
      locationToUse = {
        lng: selectedLocation.center[0],
        lat: selectedLocation.center[1]
      };
    } else if (userLocation) {
      // User location format
      locationToUse = userLocation;
    } else {
      // Default to a location if none is available (e.g., Toronto)
      locationToUse = {
        lng: -79.3832,
        lat: 43.6532
      };
    }
    
    // Check if map container exists
    const mapContainer = mapContainerRef.current;
    if (!mapContainer) {
      setMapInitialized(true); // Set to true to hide loading indicator
      return;
    }
    
    // Define an async function to initialize the map
    const initializeMap = async () => {
      try {
        // Dynamically import mapbox-gl
        const mapboxgl = await import('mapbox-gl');
        // Set access token
        mapboxgl.default.accessToken = MAPBOX_TOKEN;
      
      // If map already exists, just update it
      if (mapRef.current) {
        mapRef.current.setCenter([locationToUse.lng, locationToUse.lat]);
        
        // Update or add marker
        const markers = document.getElementsByClassName('mapboxgl-marker');
        if (markers.length > 0) {
          // Remove existing markers
          Array.from(markers).forEach(marker => marker.remove());
        }
        
        // Add new marker
        new mapboxgl.default.Marker({
          color: selectedLocation ? '#8B5CF6' : '#3FB1CE' // Purple for selected, blue for user location
        })
          .setLngLat([locationToUse.lng, locationToUse.lat])
          .addTo(mapRef.current);
          
        return;
      }
      
      // Create new map if it doesn't exist
      const map = new mapboxgl.default.Map({
        container: mapContainer,
        style: 'mapbox://styles/mapbox/streets-v11',
        center: [locationToUse.lng, locationToUse.lat],
        zoom: 14
      });
      
      // Add marker
      new mapboxgl.default.Marker({
        color: selectedLocation ? '#8B5CF6' : '#3FB1CE' // Purple for selected, blue for user location
      })
        .setLngLat([locationToUse.lng, locationToUse.lat])
        .addTo(map);
      
      // Add navigation controls
      map.addControl(new mapboxgl.default.NavigationControl(), 'top-right');
      
      // Add geolocate control
      const geolocateControl = new mapboxgl.default.GeolocateControl({
        positionOptions: {
          enableHighAccuracy: true
        },
        trackUserLocation: true
      });
      map.addControl(geolocateControl, 'top-right');
      
      // Store map reference
      mapRef.current = map;
      setMapInitialized(true);
      
      } catch (error) {
        console.error('Error loading Mapbox GL:', error);
        setMapInitialized(true); // Set to true to hide loading indicator even if there's an error
      }
    };
    
    // Call the async function
    initializeMap();
    
    // Set a timeout to ensure the loading indicator doesn't stay forever
    const timeout = setTimeout(() => {
      if (!mapInitialized) {
        console.warn('Map initialization timed out');
        setMapInitialized(true); // Force hide loading indicator after timeout
      }
    }, 5000); // 5 second timeout
    
    // Cleanup function
    return () => {
      clearTimeout(timeout);
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
    };
  }, [selectedLocation, userLocation, MAPBOX_TOKEN]);

  // Form progress calculation has been removed

  // Function to handle reordering of photos
  const onDragEnd = (result: DropResult) => {
    // Dropped outside the list
    if (!result.destination) {
      return;
    }

    const items = Array.from(uploadedFiles);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setUploadedFiles(items);
  };

  return (
    <div className="flex-1 space-y-6 sm:space-y-8 p-4 sm:p-6 md:p-8 pt-4 sm:pt-6 bg-gradient-to-b from-slate-50 to-white">
      {/* Header with progress bar */}
      <div className="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
        <div className="bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 p-6 sm:p-8 md:p-10">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4">
            <div>
              <h2 className="text-2xl sm:text-3xl font-bold tracking-tight text-white">
                Add New Property
              </h2>
              <p className="text-indigo-100 mt-2 max-w-3xl text-sm sm:text-base">
                Complete all sections below to add your property listing to the marketplace.
              </p>
            </div>
            <Link 
              href="/dashboard/properties" 
              className="inline-flex items-center gap-2 text-white bg-white/20 hover:bg-white/30 transition-colors px-4 py-2 rounded-lg text-sm font-medium self-start"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Properties
            </Link>
          </div>
          

        </div>
      </div>

      <div className="w-full max-w-5xl mx-auto px-4 sm:px-6 lg:px-0">
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 sm:space-y-8">         
          {/* Basic Information */}
          <div className="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden hover:shadow-xl transition-shadow w-full">
            <div className="border-b border-slate-100 bg-gradient-to-r from-indigo-50 to-white px-6 py-5">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-xl bg-indigo-100/80 backdrop-blur-sm shadow-sm">
                  <Info className="h-6 w-6 text-indigo-600" />
                </div>
                <div>
                  <h3 className="text-lg sm:text-xl font-semibold text-slate-900">Basic Information</h3>
                  <p className="text-sm text-slate-600 mt-1">
                    Enter the basic details of your property
                  </p>
                </div>
              </div>
            </div>
            <div className="p-6 sm:p-8 space-y-8">
              <div className="grid gap-8 px-2">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 sm:gap-10">
                  <div className="space-y-3 group/input">
                    <Label
                      htmlFor="name"
                      className="text-sm font-medium text-slate-700 group-focus-within/input:text-indigo-600 transition-colors flex items-center gap-1"
                    >
                      Title <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Input
                        id="name"
                        placeholder="e.g. Modern Apartment in Downtown"
                        {...form.register("title")}
                        className="border-slate-200 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500/20 shadow-md rounded-lg pl-11 py-6 text-base"
                      />
                      <div className="absolute left-3 top-1/2 -translate-y-1/2 h-6 w-6 rounded-full bg-indigo-50 flex items-center justify-center">
                        <Home className="h-3.5 w-3.5 text-indigo-500" />
                      </div>
                    </div>
                    {form.formState.errors.title && (
                      <p className="text-sm text-red-500 flex items-center gap-1.5 mt-1.5"><X className="h-3.5 w-3.5" /> {form.formState.errors.title.message}</p>
                    )}
                  </div>

                  <div className="space-y-3 group/input">
                    <Label
                      htmlFor="type"
                      className="text-sm font-medium text-slate-700 group-focus-within/input:text-indigo-600 transition-colors flex items-center gap-1"
                    >
                      Property Type <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Select onValueChange={(value) => form.setValue("type", value)}>
                        <SelectTrigger 
                          className="border-slate-200 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500/20 shadow-md rounded-lg pl-11 py-6 text-base"
                        >
                          <SelectValue placeholder="Select property type" />
                        </SelectTrigger>
                        <SelectContent className="border-slate-200 shadow-lg rounded-lg overflow-hidden">
                          <SelectItem value="CONDO" className="py-3 px-4 text-base hover:bg-indigo-50">Condo</SelectItem>
                          <SelectItem value="HOUSE" className="py-3 px-4 text-base hover:bg-indigo-50">House</SelectItem>
                          <SelectItem value="LOFT_STUDIO" className="py-3 px-4 text-base hover:bg-indigo-50">Loft/Studio</SelectItem>
                        </SelectContent>
                      </Select>
                      <div className="absolute left-3 top-1/2 -translate-y-1/2 h-6 w-6 rounded-full bg-indigo-50 flex items-center justify-center">
                        <Building2 className="h-3.5 w-3.5 text-indigo-500" />
                      </div>
                    </div>
                    {form.formState.errors.type && (
                      <p className="text-sm text-red-500 flex items-center gap-1.5 mt-1.5"><X className="h-3.5 w-3.5" /> {form.formState.errors.type.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-3 group/input">
                  <Label
                    htmlFor="description"
                    className="text-sm font-medium text-slate-700 group-focus-within/input:text-indigo-600 transition-colors flex items-center gap-1"
                  >
                    Description <span className="text-red-500">*</span>
                  </Label>
                  <div className="relative">
                    <Textarea
                      id="description"
                      {...form.register("description")}
                      placeholder="Describe your property, highlighting key features and amenities..."
                      className="border-slate-200 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500/20 shadow-md rounded-lg min-h-[150px] resize-none pt-5 pl-11 text-base"
                    />
                    <div className="absolute left-3 top-3 h-6 w-6 rounded-full bg-indigo-50 flex items-center justify-center">
                      <FileText className="h-3.5 w-3.5 text-indigo-500" />
                    </div>
                  </div>
                  {form.formState.errors.description && (
                    <p className="text-sm text-red-500 flex items-center gap-1.5 mt-1.5"><X className="h-3.5 w-3.5" /> {form.formState.errors.description.message}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Location */}
          <div className="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden hover:shadow-xl transition-shadow w-full">
            <div className="border-b border-slate-100 bg-gradient-to-r from-blue-50 to-white px-6 py-5">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-xl bg-blue-100/80 backdrop-blur-sm shadow-sm">
                  <MapPin className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg sm:text-xl font-semibold text-slate-900">Location Details</h3>
                  <p className="text-sm text-slate-600 mt-1">
                    Specify where your property is located
                  </p>
                </div>
              </div>
            </div>
            <div className="p-6 sm:p-8 space-y-8">
              <div className="grid gap-8 px-2">
                <div className="space-y-3 group/input">
                  <Label
                    htmlFor="address-search"
                    className="text-sm font-medium text-slate-700 group-focus-within/input:text-indigo-600 transition-colors flex items-center gap-1"
                  >
                    Search Address <span className="text-red-500">*</span>
                  </Label>
                  <div className="relative">
                    <Input
                      id="address-search"
                      value={addressQuery}
                      onChange={(e) => setAddressQuery(e.target.value)}
                      placeholder="Search for an address or location"
                      className="border-slate-200 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500/20 shadow-md rounded-lg pl-11 py-6 text-base"
                    />
                    <div className="absolute left-3 top-1/2 -translate-y-1/2 h-6 w-6 rounded-full bg-blue-50 flex items-center justify-center">
                      {isLoadingAddress ? (
                        <Loader2 className="h-3.5 w-3.5 animate-spin text-blue-500" />
                      ) : (
                        <MapPin className="h-3.5 w-3.5 text-blue-500" />
                      )}
                    </div>
                    {addressSuggestions.length > 0 && (
                      <div className="absolute z-10 w-full mt-2 bg-white rounded-xl shadow-xl border border-slate-200 divide-y divide-slate-100 max-h-80 overflow-y-auto">
                        {addressSuggestions.map((suggestion) => (
                          <button
                            key={suggestion.id}
                            onClick={() => handleAddressSelect(suggestion)}
                            className="w-full px-5 py-4 text-left hover:bg-blue-50 transition-colors flex items-start gap-4 focus:outline-none focus:bg-blue-50"
                          >
                            <div className="p-2 rounded-full bg-blue-100 flex-shrink-0 mt-0.5">
                              <MapPin className="h-4 w-4 text-blue-600" />
                            </div>
                            <div>
                              <p className="text-sm font-medium text-slate-900">{suggestion.place_name.split(',')[0]}</p>
                              <p className="text-xs text-slate-500 mt-1">
                                {suggestion.place_name.split(',').slice(1).join(',')}
                              </p>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                  {addressError && (
                    <p className="text-sm text-red-500">{addressError}</p>
                  )}
                </div>

                <div className="space-y-3 group/input">
                  <Label
                    htmlFor="unitNumber"
                    className="text-sm font-medium text-slate-700 group-focus-within/input:text-indigo-600 transition-colors flex items-center gap-1"
                  >
                    Unit/Apartment Number
                  </Label>
                  <div className="relative">
                    <Input
                      id="unitNumber"
                      {...form.register("unitNumber")}
                      placeholder="e.g. Apt 4B, Unit 201"
                      className="border-slate-200 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500/20 shadow-md rounded-lg pl-11 py-6 text-base"
                    />
                    <div className="absolute left-3 top-1/2 -translate-y-1/2 h-6 w-6 rounded-full bg-blue-50 flex items-center justify-center">
                      <Building2 className="h-3.5 w-3.5 text-blue-500" />
                    </div>
                  </div>
                  {form.formState.errors.unitNumber && (
                    <p className="text-sm text-red-500 flex items-center gap-1.5 mt-1.5"><X className="h-3.5 w-3.5" /> {form.formState.errors.unitNumber.message}</p>
                  )}
                </div>

                {/* Hidden fields for city, province, and ZIP */}
                <input type="hidden" {...form.register("city")} />
                <input type="hidden" {...form.register("province")} />
                <input type="hidden" {...form.register("zip")} />
                
              

                {/* Map Display */}
                <div className="mt-6 relative">
                  <div
                    ref={mapContainerRef}
                    className="h-[300px] sm:h-[350px] md:h-[400px] w-full bg-slate-100 rounded-xl overflow-hidden border border-slate-200 shadow-lg mt-4 sm:mt-6"
                  />
                  {!mapInitialized && (
                    <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center bg-slate-100/80 backdrop-blur-sm z-10">
                      <div className="text-center p-6 bg-white rounded-xl shadow-lg border border-slate-200">
                        <div className="relative mx-auto w-16 h-16 mb-4">
                          <div className="w-16 h-16 rounded-full border-3 border-blue-600 border-t-transparent animate-spin"></div>
                          <MapPin className="h-8 w-8 text-blue-600 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
                        </div>
                        <p className="text-slate-700 font-medium text-lg">Loading map...</p>
                      </div>
                    </div>
                  )}
                </div>
                <div className="bg-blue-50 rounded-lg p-3 mt-3 border border-blue-100">
                  <p className="text-sm text-blue-700 flex items-center gap-2">
                    <Info className="h-4 w-4 text-blue-500" />
                    <span>The map will show your current location by default. Select an address above to update the map location.</span>
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Property Details */}
          <div className="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden hover:shadow-xl transition-shadow w-full">
            <div className="border-b border-slate-100 bg-gradient-to-r from-green-50 to-white px-6 py-5">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-xl bg-green-100/80 backdrop-blur-sm shadow-sm">
                  <Building2 className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h3 className="text-lg sm:text-xl font-semibold text-slate-900">Property Details</h3>
                  <p className="text-sm text-slate-600 mt-1">
                    Provide specific details about your property
                  </p>
                </div>
              </div>
            </div>
            <div className="p-6 sm:p-8 space-y-8">
              <div className="grid gap-8 px-2">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-3 group/input">
                    <Label
                      htmlFor="bedrooms"
                      className="text-sm font-medium text-slate-700 group-focus-within/input:text-indigo-600 transition-colors flex items-center gap-1"
                    >
                      Bedrooms <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Input
                        id="bedrooms"
                        {...form.register("bedrooms")}
                        placeholder="Number of bedrooms"
                        className="border-slate-200 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500/20 shadow-md rounded-lg pl-11 py-6 text-base"
                        type="number"
                        min="0"
                        step="1"
                        onKeyDown={(e) => {
                          // Allow only numbers and specific control keys
                          if (!/^\d$/.test(e.key) && 
                              !['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight', 'Home', 'End'].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                      />
                      <div className="absolute left-3 top-1/2 -translate-y-1/2 h-6 w-6 rounded-full bg-green-50 flex items-center justify-center">
                        <BedDouble className="h-3.5 w-3.5 text-green-500" />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2.5 group/input">
                    <Label
                      htmlFor="bathrooms"
                      className="text-sm font-medium text-slate-700 group-focus-within/input:text-indigo-600 transition-colors"
                    >
                      Bathrooms <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Bath className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-slate-400" />
                      <Input
                        id="bathrooms"
                        {...form.register("bathrooms")}
                        placeholder="Number of bathrooms"
                        className="border-slate-200 focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500 shadow-sm pl-10"
                        type="number"
                        min="0"
                        step="1"
                        onKeyDown={(e) => {
                          // Allow only numbers and specific control keys
                          if (!/^\d$/.test(e.key) && 
                              !['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight', 'Home', 'End'].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                      />
                    </div>
                  </div>

                  <div className="space-y-2.5 group/input">
                    <Label
                      htmlFor="price"
                      className="text-sm font-medium text-slate-700 group-focus-within/input:text-indigo-600 transition-colors"
                    >
                      Monthly Rent <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-slate-400" />
                      <Input
                        id="price"
                        {...form.register("price")}
                        placeholder="Enter monthly rent"
                        className="border-slate-200 focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500 shadow-sm pl-10"
                        type="number"
                        min="0"
                        step="0.01"
                        onKeyDown={(e) => {
                          // Allow only numbers, decimal point, and specific control keys
                          if (!/[\d.]/.test(e.key) && 
                              !['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight', 'Home', 'End'].includes(e.key)) {
                            e.preventDefault();
                          }
                          
                          // Prevent multiple decimal points
                          if (e.key === '.' && (e.target as HTMLInputElement).value.includes('.')) {
                            e.preventDefault();
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* Commodities */}
                <div className="space-y-4 pt-4">
                  <h4 className="text-sm font-medium text-slate-900">Commodities</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="petFriendly" {...form.register("isPetFriendly")} />
                      <Label htmlFor="petFriendly" className="text-sm text-slate-600">
                        Pet Friendly
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="smokingAllowed" {...form.register("isSmokingAllowed")} />
                      <Label htmlFor="smokingAllowed" className="text-sm text-slate-600">
                        Smoking Allowed
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="furnished" {...form.register("isFurnished")} />
                      <Label htmlFor="furnished" className="text-sm text-slate-600">
                        Furnished
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="semiFurnished" {...form.register("isSemiFurnished")} />
                      <Label htmlFor="semiFurnished" className="text-sm text-slate-600">
                        Semi-Furnished
                      </Label>
                    </div>
                  </div>
                  
                  {/* Required Verifications */}
                  <div className="space-y-4 pt-4 col-span-2">
                    <h4 className="text-sm font-medium text-slate-900">Required Tenant Verifications</h4>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="id_verification" 
                          checked={verifications.id_verification}
                          onCheckedChange={(checked) => handleVerificationChange('id_verification', checked === true)}
                        />
                        <Label htmlFor="id_verification" className="text-sm text-slate-600">
                          ID Verification
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="credit_score" 
                          checked={verifications.credit_score}
                          onCheckedChange={(checked) => handleVerificationChange('credit_score', checked === true)}
                        />
                        <Label htmlFor="credit_score" className="text-sm text-slate-600">
                          Credit Score
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="background_check" 
                          checked={verifications.background_check}
                          onCheckedChange={(checked) => handleVerificationChange('background_check', checked === true)}
                        />
                        <Label htmlFor="background_check" className="text-sm text-slate-600">
                          Background Check
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="income_verification" 
                          checked={verifications.income_verification}
                          onCheckedChange={(checked) => handleVerificationChange('income_verification', checked === true)}
                        />
                        <Label htmlFor="income_verification" className="text-sm text-slate-600">
                          Income Verification
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="rental_history" 
                          checked={verifications.rental_history}
                          onCheckedChange={(checked) => handleVerificationChange('rental_history', checked === true)}
                        />
                        <Label htmlFor="rental_history" className="text-sm text-slate-600">
                          Rental History
                        </Label>
                      </div>
                    </div>
                    <p className="text-xs text-slate-500">Select the verifications required from potential tenants</p>
                  </div>
                </div>

                {/* Availability */}
                <div className="space-y-4 pt-4">
                  <h4 className="text-sm font-medium text-slate-900">Availability</h4>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="isFreeNow"
                        checked={form.watch("isFreeNow")}
                        onCheckedChange={(checked) => {
                          form.setValue("isFreeNow", checked)
                          if (checked) {
                            form.setValue("startDate", undefined)
                          }
                        }}
                      />
                      <Label htmlFor="isFreeNow" className="text-sm text-slate-600">
                        Available Now
                      </Label>
                    </div>

                    {!form.watch("isFreeNow") && (
                      <div className="space-y-2.5 group/input">
                        <Label
                          htmlFor="startDate"
                          className="text-sm font-medium text-slate-700 group-focus-within/input:text-indigo-600 transition-colors"
                        >
                          Available From <span className="text-red-500">*</span>
                        </Label>
                        <div className="relative">
                          <CalendarIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-slate-400" />
                          <Popover>
                            <PopoverTrigger asChild>
                              <div className="relative w-full">
                                <Input
                                  readOnly
                                  value={form.watch("startDate") ? format(form.watch("startDate"), "PPP") : "Select availability date"}
                                  className="border-slate-200 focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500 shadow-sm pl-10"
                                />
                              </div>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={form.watch("startDate")}
                                onSelect={(date) => form.setValue("startDate", date)}
                                disabled={(date) => date < new Date()}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Photos */}
          <div className="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden hover:shadow-xl transition-shadow w-full">
            <div className="border-b border-slate-100 bg-gradient-to-r from-purple-50 to-white px-6 py-5">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-xl bg-purple-100/80 backdrop-blur-sm shadow-sm">
                  <ImageIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="text-lg sm:text-xl font-semibold text-slate-900">Property Photos</h3>
                  <p className="text-sm text-slate-600 mt-1">
                    Upload photos of your property (maximum 10)
                  </p>
                </div>
              </div>
            </div>
            <div className="p-6 sm:p-8 space-y-8">
              <div className="grid gap-8 px-2">
                <div 
                  {...getRootProps()} 
                  className="rounded-xl border-2 border-dashed border-purple-200 p-10 transition-all hover:bg-purple-50/50 hover:border-purple-300 cursor-pointer text-center group shadow-sm hover:shadow-md"
                >
                  <input {...getInputProps()} />
                  <div className="flex flex-col items-center justify-center gap-4">
                    <div className="p-4 rounded-full bg-purple-100 group-hover:bg-purple-200 transition-colors shadow-sm">
                      <Upload className="h-8 w-8 text-purple-600" />
                    </div>
                    <h3 className="text-xl font-medium text-slate-900 group-hover:text-purple-700 transition-colors">Upload Property Photos</h3>
                    <p className="text-sm text-slate-600 max-w-md mx-auto">
                      Drag and drop your property photos here, or click to select files. 
                      We recommend high-quality images that showcase your property's best features.
                    </p>
                    <Button 
                      type="button" 
                      className="mt-3 border border-purple-200 bg-purple-50 text-purple-600 hover:bg-purple-100 hover:border-purple-300 hover:text-purple-700 rounded-lg h-12 px-6 py-3 font-medium shadow-sm hover:shadow transition-all"
                    >
                      Select Files
                    </Button>
                    <p className="text-xs text-slate-400 mt-2">
                      Supported formats: JPG, PNG, WEBP. Max file size: 5MB.
                    </p>
                  </div>
                </div>

                <div className="mt-8">
                  {uploadedFiles.length > 0 && (
                    <div className="space-y-5">
                      <div className="flex items-center justify-between">
                        <h4 className="text-lg font-medium text-slate-800 flex items-center gap-2">
                          <ImageIcon className="h-5 w-5 text-purple-500" />
                          Uploaded Photos
                          <Badge variant="outline" className="ml-2 bg-purple-50 text-purple-700 border-purple-200 px-2.5 py-0.5">
                            {uploadedFiles.length}
                          </Badge>
                        </h4>
                        <p className="text-sm text-slate-600 flex items-center gap-1.5">
                          <MoveHorizontal className="h-4 w-4 text-purple-500" />
                          Drag and drop to reorder photos. The first photo will be the main listing image.
                        </p>
                      </div>
                      
                      <DragDropContext onDragEnd={onDragEnd}>
                        <Droppable droppableId="photos" direction="horizontal">
                          {(provided) => (
                            <div 
                              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5"
                              {...provided.droppableProps}
                              ref={provided.innerRef}
                            >
                              {uploadedFiles.map((file, index) => (
                                <Draggable key={index.toString()} draggableId={index.toString()} index={index}>
                                  {(provided, snapshot) => (
                                    <div 
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      {...provided.dragHandleProps}
                                      className={`group relative rounded-xl overflow-hidden border border-slate-200 shadow-md hover:shadow-lg transition-all ${snapshot.isDragging ? 'ring-2 ring-purple-500 opacity-90 rotate-1 scale-105' : ''}`}
                                    >
                                      <div className="aspect-video bg-slate-100 relative">
                                        {index === 0 && (
                                          <div className="absolute top-3 left-3 z-10 bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-xs font-medium px-3 py-1.5 rounded-full shadow-md">
                                            Cover Photo
                                          </div>
                                        )}
                                        <Image
                                          src={file.preview || URL.createObjectURL(file)}
                                          alt={`Property photo ${index + 1}`}
                                          fill
                                          className="object-cover"
                                        />
                                        <div className="absolute bottom-3 right-3 bg-white/80 backdrop-blur-sm text-slate-700 text-xs px-2 py-1 rounded-md shadow-sm">
                                          Photo {index + 1}
                                        </div>
                                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                                          <div className="flex gap-3">
                                            <Button
                                              type="button"
                                              variant="destructive"
                                              size="sm"
                                              className="h-9 px-4 rounded-lg shadow-lg hover:scale-105 transition-transform"
                                              onClick={() => removeFile(index)}
                                            >
                                              <Trash2 className="h-4 w-4 mr-1.5" />
                                              Remove
                                            </Button>
                                          </div>
                                        </div>
                                      </div>
                                      <div className="p-3 bg-white">
                                        <div className="flex items-center justify-between">
                                          <p className="text-sm font-medium text-slate-700 truncate">{file.name}</p>
                                          <div className="flex items-center gap-1 text-slate-400">
                                            <span className="text-xs">{index + 1}</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M12 5v14M5 12h14"></path></svg>
                                          </div>
                                        </div>
                                        <p className="text-xs text-slate-500">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                                      </div>
                                    </div>
                                  )}
                                </Draggable>
                              ))}
                              {provided.placeholder}
                            </div>
                          )}
                        </Droppable>
                      </DragDropContext>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Requirements */}
          

          {/* Submit Button */}
          <div className="flex flex-col-reverse sm:flex-row justify-end items-center gap-3 sm:gap-4 bg-white rounded-xl shadow-sm border border-slate-200 p-4 sm:p-6">
            <Button
              type="button"
              className="px-4 sm:px-6 py-2 h-auto border border-purple-200 bg-purple-50 text-purple-600 hover:bg-purple-100 hover:border-purple-300 hover:text-purple-700 rounded-md font-medium w-full sm:w-auto"
              onClick={() => window.history.back()}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="w-full sm:w-auto px-4 sm:px-8 py-2.5 h-auto bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-md rounded-lg"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Add Property
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}