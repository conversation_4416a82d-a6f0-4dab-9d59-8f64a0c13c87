import { NextRequest, NextResponse } from 'next/server'

// Mock data for development - replace with actual database queries
const mockProperties = [
  {
    id: "1",
    title: "Modern Downtown Apartment",
    address: "123 Rue Saint-Catherine, Montreal, QC H3B 1A7",
    price: 1800,
    bedrooms: 2,
    bathrooms: 1,
    parking: 1,
    area: 850,
    images: ["/api/placeholder/800/600", "/api/placeholder/800/600"],
    type: "Apartment",
    status: "LIVE",
    description: "Beautiful modern apartment in the heart of downtown Montreal with stunning city views.",
    amenities: ["Hardwood floors", "Stainless steel appliances", "In-unit laundry", "Air conditioning", "Balcony", "Gym access"],
    landlord: {
      name: "Property Management Inc.",
      phone: "(*************",
      email: "<EMAIL>"
    },
    availableDate: "2024-02-01",
    coordinates: { lat: 45.5017, lng: -73.5673 }
  },
  {
    id: "2",
    title: "Cozy Studio in Plateau",
    address: "456 Avenue Mont-Royal, Montreal, QC H2T 1V6",
    price: 1200,
    bedrooms: 1,
    bathrooms: 1,
    parking: 0,
    area: 450,
    images: ["/api/placeholder/800/600", "/api/placeholder/800/600"],
    type: "Studio",
    status: "LIVE",
    description: "Charming studio apartment in trendy Plateau neighborhood with exposed brick walls.",
    amenities: ["Exposed brick", "High ceilings", "Natural light", "Close to metro"],
    landlord: {
      name: "Plateau Properties",
      phone: "(*************",
      email: "<EMAIL>"
    },
    availableDate: "2024-01-15",
    coordinates: { lat: 45.5276, lng: -73.5785 }
  },
  {
    id: "3",
    title: "Spacious Family Home",
    address: "789 Rue de la Paix, Quebec City, QC G1R 2L5",
    price: 2500,
    bedrooms: 3,
    bathrooms: 2,
    parking: 2,
    area: 1200,
    images: ["/api/placeholder/800/600", "/api/placeholder/800/600"],
    type: "House",
    status: "LIVE",
    description: "Perfect family home with garden and garage in quiet neighborhood.",
    amenities: ["Private garden", "Garage", "Fireplace", "Updated kitchen", "Hardwood floors"],
    landlord: {
      name: "Quebec Family Homes",
      phone: "(*************",
      email: "<EMAIL>"
    },
    availableDate: "2024-03-01",
    coordinates: { lat: 46.8139, lng: -71.2080 }
  }
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '6')

    // Return featured properties (for now, just return the first N properties)
    // In a real application, you would query properties marked as "featured" in the database
    const featuredProperties = mockProperties.slice(0, limit)

    return NextResponse.json(featuredProperties)
  } catch (error) {
    console.error('Error in featured properties API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
