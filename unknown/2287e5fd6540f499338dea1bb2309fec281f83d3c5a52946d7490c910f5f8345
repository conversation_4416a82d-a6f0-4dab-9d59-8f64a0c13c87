"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  BarChart3, 
  TrendingUp, 
  DollarSign, 
  Home, 
  Calendar,
  ArrowLeft,
  Download,
  Filter,
  RefreshCw
} from "lucide-react"
import Link from "next/link"

export default function PropertyAnalyticsPage() {
  const [timeRange, setTimeRange] = useState("6months")
  const [propertyType, setPropertyType] = useState("all")

  const analyticsData = [
    {
      title: "Revenue Performance",
      value: "$45,230",
      change: "+12.5%",
      description: "Total rental income",
      icon: DollarSign,
      color: "text-green-600"
    },
    {
      title: "Occupancy Rate",
      value: "94.2%",
      change: "+2.1%",
      description: "Average across portfolio",
      icon: Home,
      color: "text-blue-600"
    },
    {
      title: "Average ROI",
      value: "8.7%",
      change: "+0.8%",
      description: "Return on investment",
      icon: TrendingUp,
      color: "text-purple-600"
    },
    {
      title: "Maintenance Costs",
      value: "$3,420",
      change: "-5.2%",
      description: "Monthly average",
      icon: Calendar,
      color: "text-orange-600"
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/dashboard/market-data">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Market Data
              </Button>
            </Link>
            <div>
              <h2 className="text-4xl font-bold tracking-tight text-gray-900">Property Analytics</h2>
              <p className="text-gray-500 text-lg">
                Detailed performance analytics for your property portfolio
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Analytics Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <label className="text-sm font-medium text-gray-700 mb-2 block">Time Range</label>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1month">Last Month</SelectItem>
                  <SelectItem value="3months">Last 3 Months</SelectItem>
                  <SelectItem value="6months">Last 6 Months</SelectItem>
                  <SelectItem value="1year">Last Year</SelectItem>
                  <SelectItem value="2years">Last 2 Years</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1">
              <label className="text-sm font-medium text-gray-700 mb-2 block">Property Type</label>
              <Select value={propertyType} onValueChange={setPropertyType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select property type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Properties</SelectItem>
                  <SelectItem value="condo">Condos</SelectItem>
                  <SelectItem value="house">Houses</SelectItem>
                  <SelectItem value="loft">Lofts/Studios</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {analyticsData.map((metric, index) => (
          <Card key={index} className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {metric.title}
              </CardTitle>
              <metric.icon className={`h-4 w-4 ${metric.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{metric.value}</div>
              <div className="flex items-center gap-1 text-xs">
                <TrendingUp className="h-3 w-3 text-green-500" />
                <span className="text-green-600">{metric.change}</span>
                <span className="text-gray-500">{metric.description}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Revenue Chart */}
        <Card className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              Revenue Trends
            </CardTitle>
            <CardDescription>
              Monthly revenue performance over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">Revenue chart will be displayed here</p>
                <p className="text-sm text-gray-400">Integration with charting library needed</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Occupancy Chart */}
        <Card className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Home className="h-5 w-5 text-green-600" />
              Occupancy Rates
            </CardTitle>
            <CardDescription>
              Property occupancy trends and patterns
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <Home className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">Occupancy chart will be displayed here</p>
                <p className="text-sm text-gray-400">Integration with charting library needed</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Property Performance Table */}
      <Card className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
        <CardHeader>
          <CardTitle>Property Performance Breakdown</CardTitle>
          <CardDescription>
            Individual property analytics and performance metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((property) => (
              <div key={property} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Home className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Property {property}</h4>
                    <p className="text-sm text-gray-500">123 Main St, City</p>
                  </div>
                </div>
                <div className="flex items-center gap-6">
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Revenue</p>
                    <p className="font-semibold text-gray-900">$2,500</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Occupancy</p>
                    <p className="font-semibold text-gray-900">100%</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">ROI</p>
                    <p className="font-semibold text-green-600">8.5%</p>
                  </div>
                  <Badge className="bg-green-100 text-green-800">Performing Well</Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
