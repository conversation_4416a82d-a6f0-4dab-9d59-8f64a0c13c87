"use client"

import { useEffect, useState } from 'react'
import Cookies from 'js-cookie'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { getAuthUrl } from '@/lib/auth.client'

export default function DebugAuthPage() {
  const [authState, setAuthState] = useState({
    cookies: {},
    urlHash: '',
    urlParams: {},
    localStorage: {},
    sessionStorage: {}
  })

  useEffect(() => {
    const updateAuthState = () => {
      // Get all cookies
      const allCookies = {
        session: Cookies.get('session'),
        id_token: Cookies.get('id_token'),
        access_token: Cookies.get('access_token'),
        refresh_token: Cookies.get('refresh_token')
      }

      // Get URL hash
      const hash = window.location.hash.substring(1)
      
      // Parse hash params
      const hashParams = hash ? hash.split("&").reduce((result: Record<string, string>, item) => {
        const parts = item.split("=");
        if (parts.length === 2) {
          result[parts[0]] = decodeURIComponent(parts[1]);
        }
        return result;
      }, {}) : {}

      // Get localStorage items
      const localStorageItems = {
        authRedirect: localStorage.getItem('authRedirect'),
        sidebarCollapsed: localStorage.getItem('sidebarCollapsed')
      }

      // Get sessionStorage items
      const sessionStorageItems = {
        cognito_auth_state: sessionStorage.getItem('cognito_auth_state'),
        isAuthenticated: sessionStorage.getItem('isAuthenticated')
      }

      setAuthState({
        cookies: allCookies,
        urlHash: hash,
        urlParams: hashParams,
        localStorage: localStorageItems,
        sessionStorage: sessionStorageItems
      })
    }

    updateAuthState()
    
    // Update every second to see real-time changes
    const interval = setInterval(updateAuthState, 1000)
    
    return () => clearInterval(interval)
  }, [])

  const handleLogin = async () => {
    try {
      const authUrl = await getAuthUrl(true)
      console.log('Redirecting to:', authUrl)
      window.location.href = authUrl
    } catch (error) {
      console.error('Error getting auth URL:', error)
    }
  }

  const clearAllAuth = () => {
    // Clear all cookies
    Cookies.remove('session')
    Cookies.remove('id_token')
    Cookies.remove('access_token')
    Cookies.remove('refresh_token')
    
    // Clear storage
    localStorage.removeItem('authRedirect')
    sessionStorage.removeItem('cognito_auth_state')
    sessionStorage.removeItem('isAuthenticated')
    
    console.log('All auth data cleared')
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Authentication Debug</h1>
        <div className="space-x-2">
          <Button onClick={handleLogin}>Test Login</Button>
          <Button variant="destructive" onClick={clearAllAuth}>Clear All Auth</Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Cookies</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto">
              {JSON.stringify(authState.cookies, null, 2)}
            </pre>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>URL Hash Fragment</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <strong>Raw Hash:</strong>
                <pre className="text-sm bg-gray-100 p-2 rounded mt-1">
                  {authState.urlHash || 'No hash fragment'}
                </pre>
              </div>
              <div>
                <strong>Parsed Params:</strong>
                <pre className="text-sm bg-gray-100 p-2 rounded mt-1">
                  {JSON.stringify(authState.urlParams, null, 2)}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Local Storage</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto">
              {JSON.stringify(authState.localStorage, null, 2)}
            </pre>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Session Storage</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto">
              {JSON.stringify(authState.sessionStorage, null, 2)}
            </pre>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Current URL Info</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify({
              href: typeof window !== 'undefined' ? window.location.href : 'N/A',
              pathname: typeof window !== 'undefined' ? window.location.pathname : 'N/A',
              search: typeof window !== 'undefined' ? window.location.search : 'N/A',
              hash: typeof window !== 'undefined' ? window.location.hash : 'N/A',
              origin: typeof window !== 'undefined' ? window.location.origin : 'N/A'
            }, null, 2)}
          </pre>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>All Document Cookies</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto">
            {typeof document !== 'undefined' ? document.cookie || 'No cookies found' : 'N/A'}
          </pre>
        </CardContent>
      </Card>
    </div>
  )
}
