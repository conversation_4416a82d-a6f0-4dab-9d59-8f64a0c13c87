/**
 * User and profile validation schemas
 */

import { z } from 'zod';
import { emailSchema, phoneSchema, urlSchema, businessTypeSchema, languageSchema } from './common.validations';

export const updatePersonalInfoSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters').optional(),
  lastName: z.string().min(2, 'Last name must be at least 2 characters').optional(),
  businessName: z.string().min(2, 'Business name must be at least 2 characters').optional(),
  phone: phoneSchema.optional(),
  website: urlSchema,
  address: z.string().min(5, 'Address must be at least 5 characters').optional(),
});

export const businessHourSchema = z.object({
  dayOfWeek: z.string().min(1, 'Day of week is required'),
  openTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
  closeTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
  isClosed: z.boolean(),
});

export const updateBusinessHoursSchema = z.object({
  businessHours: z.array(businessHourSchema),
});

export const verificationSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  dateOfBirth: z.string().refine((date) => {
    const birthDate = new Date(date);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    return age >= 18;
  }, 'You must be at least 18 years old'),
  documentType: z.string().min(1, 'Document type is required'),
  documentNumber: z.string().min(1, 'Document number is required'),
  expiryDate: z.string().refine((date) => {
    const expiryDate = new Date(date);
    const today = new Date();
    return expiryDate > today;
  }, 'Document must not be expired'),
});

export const profileSettingsSchema = z.object({
  email: emailSchema,
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  businessName: z.string().min(2, 'Business name must be at least 2 characters').optional(),
  phone: phoneSchema,
  website: urlSchema,
  businessType: businessTypeSchema,
  preferredLanguage: languageSchema,
  address: z.string().min(5, 'Address must be at least 5 characters'),
});
