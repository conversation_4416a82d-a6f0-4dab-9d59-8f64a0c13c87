import { api } from '../api-instance';
import type {
  DashboardData,
  ProfileData,
  UpdatePersonalInfoRequest,
  UpdateBusinessHoursRequest,
  UpdatePreferencesRequest
} from '@/lib/types';

/**
 * Accounts service for user account-related API calls
 */
export const accountsService = {
  /**
   * Get dashboard data for the current user
   */
  async getDashboardData() {
    return api.get<DashboardData>('/accounts/dashboard');
  },

  /**
   * Get profile settings for the current user
   */
  async getProfileSettings() {
    return api.get<ProfileData>('/accounts/settings');
  },

  /**
   * Update personal information
   * @param data Updated personal information
   */
  async updatePersonalInfo(data: UpdatePersonalInfoRequest) {
    return api.put('/accounts/settings/personal-information', data);
  },

  /**
   * Update business hours
   * @param data Updated business hours
   */
  async updateBusinessHours(data: UpdateBusinessHoursRequest) {
    return api.put('/accounts/settings/business-hours', data);
  },

  /**
   * Update user preferences
   * @param data Updated preferences
   */
  async updatePreferences(data: UpdatePreferencesRequest) {
    return api.put('/accounts/settings/preferences', data);
  },

  /**
   * Update user password
   * @param currentPassword Current password
   * @param newPassword New password
   */
  async updatePassword(currentPassword: string, newPassword: string) {
    return api.put('/accounts/settings/password', {
      currentPassword,
      newPassword,
    });
  }
};

// Re-export types for convenience
export type { DashboardData, ProfileData, UpdatePersonalInfoRequest, UpdateBusinessHoursRequest, UpdatePreferencesRequest };

export default accountsService;
