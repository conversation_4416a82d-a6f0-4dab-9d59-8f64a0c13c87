/**
 * API-related constants
 */

export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
} as const;

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
} as const;

export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',           // POST
    LOGOUT: '/auth/logout',         // POST
    SIGNUP: '/auth/signup',         // POST
    REFRESH: '/auth/refresh',       // POST
    CALLBACK: '/auth/callback',     // GET
  },
  ACCOUNTS: {
    DASHBOARD: '/accounts/dashboard',                           // GET
    SETTINGS: '/accounts/settings',                             // GET
    PERSONAL_INFO: '/accounts/settings/personal-information',   // PUT
    BUSINESS_HOURS: '/accounts/settings/business-hours',        // PUT
  },
  PROPERTIES: {
    BASE: '/properties',            // GET (list), POST (create)
    STATUS: '/properties/status',   // PUT (update status)
    BY_ID: (id: string) => `/properties/${id}`, // GET, PUT, DELETE
  },
  VERIFICATION: {
    STATUS: '/verification/status', // GET
    SUBMIT: '/verification/submit', // POST
  },
} as const;

// Endpoint methods for better documentation and type safety
export const API_METHODS = {
  AUTH: {
    LOGIN: 'POST',
    LOGOUT: 'POST',
    SIGNUP: 'POST',
    REFRESH: 'POST',
    CALLBACK: 'GET',
  },
  ACCOUNTS: {
    DASHBOARD: 'GET',
    SETTINGS: 'GET',
    PERSONAL_INFO: 'PUT',
    BUSINESS_HOURS: 'PUT',
  },
  PROPERTIES: {
    LIST: 'GET',
    CREATE: 'POST',
    UPDATE_STATUS: 'PUT',
    GET_BY_ID: 'GET',
    UPDATE: 'PUT',
    DELETE: 'DELETE',
  },
  VERIFICATION: {
    STATUS: 'GET',
    SUBMIT: 'POST',
  },
} as const;

export const REQUEST_TIMEOUT = 30000; // 30 seconds

export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
} as const;
