import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import env from "@/lib/env";
import { COGNITO_CONFIG } from "@/lib/auth-config";

/**
 * Get the base URL for the current environment
 * This is a server-side function so we can't use window.location
 */
function getBaseUrl(request: NextRequest): string {
  // For development, we'll extract the host and port from the request
  if (env.isDev) {
    const host = request.headers.get('host') || 'localhost:3000';
    return `http://${host}`;
  }
  
  // For production and staging, use the configured base URL
  return env.BASE_URL;
}

/**
 * Logout API handler
 * Clears cookies and redirects to Cognito logout URL
 */
export async function GET(request: NextRequest) {
  // Clear all auth cookies
  const cookieStore = cookies();
  cookieStore.delete("session");
  cookieStore.delete("refresh_token");
  
  // Get the base URL for redirecting
  const baseUrl = getBaseUrl(request);
  
  // Build the Cognito logout URL
  const logoutUrl = `https://${COGNITO_CONFIG.DOMAIN}/logout?client_id=${COGNITO_CONFIG.CLIENT_ID}&logout_uri=${baseUrl}`;
  
  // Redirect to Cognito logout URL
  return NextResponse.redirect(logoutUrl);
}

/**
 * Logout API handler (POST)
 * Just clears cookies for client-side logout
 */
export async function POST() {
  // Clear all auth cookies
  const cookieStore = cookies();
  cookieStore.delete("session");
  cookieStore.delete("refresh_token");
  
  return NextResponse.json({ 
    success: true,
    message: "Logged out successfully"
  });
}