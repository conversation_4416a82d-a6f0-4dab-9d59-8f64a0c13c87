"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Home, 
  Users, 
  Calendar,
  BarChart3,
  LineChart,
  <PERSON><PERSON>hart,
  MapPin,
  Filter,
  Download,
  RefreshCw
} from "lucide-react"
import Link from "next/link"

export default function MarketDataPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  useEffect(() => {
    // Simulate loading market data
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1500)

    return () => clearTimeout(timer)
  }, [])

  const marketMetrics = [
    {
      title: "Average Rent",
      value: "$2,450",
      change: "+5.2%",
      trend: "up",
      description: "vs last month",
      icon: DollarSign,
      color: "text-green-600"
    },
    {
      title: "Vacancy Rate",
      value: "3.8%",
      change: "-0.5%",
      trend: "down",
      description: "vs last month",
      icon: Home,
      color: "text-blue-600"
    },
    {
      title: "Days on Market",
      value: "18 days",
      change: "-2 days",
      trend: "down",
      description: "vs last month",
      icon: Calendar,
      color: "text-purple-600"
    },
    {
      title: "Active Listings",
      value: "1,247",
      change: "+12%",
      trend: "up",
      description: "vs last month",
      icon: Users,
      color: "text-orange-600"
    }
  ]

  const quickActions = [
    {
      title: "Property Analytics",
      description: "Detailed analytics for your properties",
      href: "/dashboard/market-data/analytics",
      icon: BarChart3,
      color: "bg-blue-50 text-blue-600"
    },
    {
      title: "Market Trends",
      description: "View historical market trends",
      href: "/dashboard/market-data/trends",
      icon: LineChart,
      color: "bg-green-50 text-green-600"
    },
    {
      title: "Comparative Analysis",
      description: "Compare properties and markets",
      href: "/dashboard/market-data/comparison",
      icon: PieChart,
      color: "bg-purple-50 text-purple-600"
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-4xl font-bold tracking-tight text-gray-900">Market Data</h2>
            <p className="text-gray-500 text-lg">
              Real-time market insights and analytics for informed decisions
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <span>Last updated: {lastUpdated.toLocaleString()}</span>
          <Badge variant="secondary" className="bg-green-50 text-green-700">
            Live Data
          </Badge>
        </div>
      </div>

      {/* Market Metrics */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {marketMetrics.map((metric, index) => (
          <Card key={index} className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {metric.title}
              </CardTitle>
              <metric.icon className={`h-4 w-4 ${metric.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{metric.value}</div>
              <div className="flex items-center gap-1 text-xs">
                {metric.trend === "up" ? (
                  <TrendingUp className="h-3 w-3 text-green-500" />
                ) : (
                  <TrendingDown className="h-3 w-3 text-red-500" />
                )}
                <span className={metric.trend === "up" ? "text-green-600" : "text-red-600"}>
                  {metric.change}
                </span>
                <span className="text-gray-500">{metric.description}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <Card className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-gray-900">Quick Actions</CardTitle>
          <CardDescription>
            Access detailed market analysis tools
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            {quickActions.map((action, index) => (
              <Link key={index} href={action.href}>
                <Card className="cursor-pointer transition-all hover:shadow-lg hover:scale-105 border border-gray-100">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4">
                      <div className={`p-3 rounded-lg ${action.color}`}>
                        <action.icon className="h-6 w-6" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{action.title}</h3>
                        <p className="text-sm text-gray-500">{action.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Market Overview Tabs */}
      <Card className="border-0 bg-white/50 backdrop-blur-xl shadow-xl">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-gray-900">Market Overview</CardTitle>
          <CardDescription>
            Comprehensive market data and insights
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="rental">Rental Market</TabsTrigger>
              <TabsTrigger value="sales">Sales Market</TabsTrigger>
              <TabsTrigger value="regional">Regional Data</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="mt-6">
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-semibold text-gray-900 mb-2">Market Health Score</h4>
                    <div className="flex items-center gap-2">
                      <div className="text-2xl font-bold text-green-600">8.5/10</div>
                      <Badge className="bg-green-100 text-green-800">Excellent</Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">Strong demand with balanced supply</p>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-semibold text-gray-900 mb-2">Price Growth</h4>
                    <div className="flex items-center gap-2">
                      <div className="text-2xl font-bold text-blue-600">****%</div>
                      <TrendingUp className="h-4 w-4 text-green-500" />
                    </div>
                    <p className="text-sm text-gray-600 mt-1">Year-over-year growth</p>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="rental" className="mt-6">
              <div className="text-center py-8">
                <p className="text-gray-500">Rental market data will be displayed here</p>
              </div>
            </TabsContent>
            
            <TabsContent value="sales" className="mt-6">
              <div className="text-center py-8">
                <p className="text-gray-500">Sales market data will be displayed here</p>
              </div>
            </TabsContent>
            
            <TabsContent value="regional" className="mt-6">
              <div className="text-center py-8">
                <p className="text-gray-500">Regional market data will be displayed here</p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
