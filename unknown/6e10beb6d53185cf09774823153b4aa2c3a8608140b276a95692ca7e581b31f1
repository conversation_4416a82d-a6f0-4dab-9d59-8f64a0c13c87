import { jwtVerify, decodeJwt } from "jose";
import { cookies } from "next/headers";
import { NextRequest } from "next/server";
import COGNITO_CONFIG from "./auth-config";
import env from "./env";

/**
 * Get the redirect URI for the current environment
 * This is a server-side function so we need to determine the URL differently
 */
export function getServerRedirectUri(request?: NextRequest): string {
  // For development, extract from the request if available
  if (env.isDev && request) {
    const host = request.headers.get('host') || 'localhost:8081';
    // Always use http for localhost
    return `http://${host}/dashboard`;
  }
  
  // For development without request, use the default
  if (env.isDev) {
    return `http://localhost:8081/dashboard`;
  }
  
  // For production and staging
  return `${env.BASE_URL}/dashboard`;
}

/**
 * Exchange the authorization code for tokens
 * @param code Authorization code from Cognito
 * @param request Optional request object to determine the redirect URI
 * @returns Access and refresh tokens
 */
export async function exchangeCodeForTokens(code: string, request?: NextRequest) {
  try {
    // Get the redirect URI based on environment
    const redirectUri = getServerRedirectUri(request);
    
    console.log("Exchanging code for tokens with redirect URI:", redirectUri);
    
    // Build the token request
    const params = new URLSearchParams({
      grant_type: "authorization_code",
      client_id: COGNITO_CONFIG.CLIENT_ID,
      code,
      redirect_uri: redirectUri,
    });
    
    // Add client secret if available (for confidential clients)
    if (COGNITO_CONFIG.CLIENT_SECRET) {
      const authHeader = Buffer.from(
        `${COGNITO_CONFIG.CLIENT_ID}:${COGNITO_CONFIG.CLIENT_SECRET}`
      ).toString("base64");
      
      // Make the token request
      const response = await fetch(
        `https://${COGNITO_CONFIG.DOMAIN}/oauth2/token`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            Authorization: `Basic ${authHeader}`,
          },
          body: params.toString(),
          cache: "no-store",
        }
      );
      
      if (!response.ok) {
        const error = await response.text();
        console.error("Token exchange error:", error);
        throw new Error(`Token exchange failed: ${response.status} ${error}`);
      }
      
      return await response.json();
    } else {
      // Without client secret (for public clients)
      const response = await fetch(
        `https://${COGNITO_CONFIG.DOMAIN}/oauth2/token`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: params.toString(),
          cache: "no-store",
        }
      );
      
      if (!response.ok) {
        const error = await response.text();
        console.error("Token exchange error:", error);
        throw new Error(`Token exchange failed: ${response.status} ${error}`);
      }
      
      return await response.json();
    }
  } catch (error) {
    console.error("Error exchanging code for tokens:", error);
    throw error;
  }
}

/**
 * Refresh access token using a refresh token
 * @param refreshToken Refresh token
 * @returns New access token and refresh token
 */
export async function refreshTokens(refreshToken: string) {
  const params = new URLSearchParams({
    grant_type: "refresh_token",
    client_id: COGNITO_CONFIG.CLIENT_ID,
    refresh_token: refreshToken,
  });

  const basicAuth = Buffer.from(
    `${COGNITO_CONFIG.CLIENT_ID}:${COGNITO_CONFIG.CLIENT_SECRET}`
  ).toString("base64");

  const response = await fetch(
    `https://${COGNITO_CONFIG.DOMAIN}/oauth2/token`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${basicAuth}`,
      },
      body: params.toString(),
      cache: "no-store",
    }
  );

  if (!response.ok) {
    throw new Error("Failed to refresh tokens");
  }

  return response.json();
}

/**
 * Verify JWT token
 * @param token Access token to verify
 * @returns Token payload or null if invalid
 */
export async function verifyToken(token: string) {
  try {
    const { payload } = await jwtVerify(
      token,
      new TextEncoder().encode(COGNITO_CONFIG.CLIENT_SECRET)
    );
    return payload;
  } catch (error) {
    return null;
  }
}

/**
 * Get user information from token
 * @param token Access token
 * @returns User information
 */
export function getUserFromToken(token: string) {
  try {
    const decoded = decodeJwt(token);
    return {
      sub: decoded.sub,
      email: decoded.email,
      given_name: decoded.given_name,
      family_name: decoded.family_name,
      // Add other user attributes as needed
      groups: decoded['cognito:groups'] || [],
    };
  } catch (error) {
    console.error("Error decoding token:", error);
    return null;
  }
}

/**
 * Get the current session from cookies
 * @returns Session payload or null if not authenticated
 */
export async function getSession() {
  const cookieStore = cookies();
  const token = cookieStore.get("session");
  
  if (!token) {
    return null;
  }

  const payload = await verifyToken(token.value);
  
  // If token is invalid but we have a refresh token, try to refresh
  if (!payload) {
    const refreshToken = cookieStore.get("refresh_token");
    if (refreshToken) {
      try {
        const newTokens = await refreshTokens(refreshToken.value);
        
        // Set the new tokens in cookies
        cookieStore.set("session", newTokens.access_token, {
          httpOnly: true,
          path: "/",
          maxAge: 60 * 60 * 24, // 1 day
          secure: process.env.NODE_ENV === "production",
          sameSite: "lax",
        });
        
        if (newTokens.refresh_token) {
          cookieStore.set("refresh_token", newTokens.refresh_token, {
            httpOnly: true,
            path: "/",
            maxAge: 60 * 60 * 24 * 30, // 30 days
            secure: process.env.NODE_ENV === "production",
            sameSite: "lax",
          });
        }
        
        // Verify the new token
        return await verifyToken(newTokens.access_token);
      } catch (error) {
        console.error("Failed to refresh token:", error);
        return null;
      }
    }
    return null;
  }
  
  return payload;
}

/**
 * Get the current user from session
 * @returns User information or null if not authenticated
 */
export async function getCurrentUser() {
  const cookieStore = cookies();
  const token = cookieStore.get("session");
  
  if (!token) {
    return null;
  }
  
  return getUserFromToken(token.value);
}

/**
 * Validate request for middleware
 * @param request Next.js request
 * @returns Whether the request is authenticated
 */
export async function validateRequest(request: NextRequest) {
  try {
    // Get the session token from the request cookies
    const sessionCookie = request.cookies.get("session");
    
    if (!sessionCookie) {
      console.log("No session cookie found");
      return false;
    }
    
    // Verify the token
    const payload = await verifyToken(sessionCookie.value);
    
    if (!payload) {
      console.log("Invalid session token");
      
      // Try to refresh the token if we have a refresh token
      const refreshTokenCookie = request.cookies.get("refresh_token");
      
      if (refreshTokenCookie) {
        try {
          // Attempt to refresh the token
          const newTokens = await refreshTokens(refreshTokenCookie.value);
          
          // If successful, consider the request authenticated
          // The actual token update will happen in the API route
          return !!newTokens.access_token;
        } catch (error) {
          console.error("Failed to refresh token:", error);
          return false;
        }
      }
      
      return false;
    }
    
    return true;
  } catch (error) {
    console.error("Error validating request:", error);
    return false;
  }
}