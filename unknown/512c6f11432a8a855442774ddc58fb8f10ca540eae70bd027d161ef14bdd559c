/**
 * Authentication validation schemas
 */

import { z } from 'zod';

export const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number');

export const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(1, 'Password is required'),
});

export const baseSignupSchema = {
  email: z.string().email('Please enter a valid email address'),
  preferredLang: z.boolean().default(false),
  password: passwordSchema,
  confirmPassword: z.string(),
};

export const signupSchema = z.object({
  ...baseSignupSchema,
  accountType: z.enum(['individual', 'company'] as const),
  firstName: z.string().min(2, 'First name must be at least 2 characters').optional(),
  lastName: z.string().min(2, 'Last name must be at least 2 characters').optional(),
  companyName: z.string().min(2, 'Company name must be at least 2 characters').optional(),
}).refine(
  (data) => {
    if (data.accountType === 'individual') {
      return data.firstName && data.lastName;
    } else {
      return data.companyName;
    }
  },
  {
    message: 'Please fill in all required fields',
  }
).refine(
  (data) => data.password === data.confirmPassword,
  {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  }
);

export const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

export const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: passwordSchema,
  confirmPassword: z.string(),
}).refine(
  (data) => data.password === data.confirmPassword,
  {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  }
);
