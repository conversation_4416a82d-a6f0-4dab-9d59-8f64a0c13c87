/**
 * Maintenance Service
 * 
 * Centralized service for all maintenance-related API calls
 */

import { api } from '../api-instance'
import type { ApiResponse, PaginatedResponse } from '@/lib/types'

// Maintenance Types
export interface MaintenanceRequest {
  id: string
  propertyId: string
  tenantId?: string
  title: string
  description: string
  category: 'PLUMBING' | 'ELECTRICAL' | 'HVAC' | 'APPLIANCES' | 'STRUCTURAL' | 'COSMETIC' | 'OTHER'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'EMERGENCY'
  status: 'OPEN' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'
  assignedTo?: string
  estimatedCost?: number
  actualCost?: number
  scheduledDate?: string
  completedDate?: string
  images?: string[]
  notes?: string
  createdAt: string
  updatedAt: string
  property?: {
    id: string
    title: string
    address: string
  }
  tenant?: {
    id: string
    firstName: string
    lastName: string
    email: string
    phone: string
  }
}

export interface CreateMaintenanceRequest {
  propertyId: string
  tenantId?: string
  title: string
  description: string
  category: 'PLUMBING' | 'ELECTRICAL' | 'HVAC' | 'APPLIANCES' | 'STRUCTURAL' | 'COSMETIC' | 'OTHER'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'EMERGENCY'
  estimatedCost?: number
  scheduledDate?: string
  images?: File[]
}

export interface UpdateMaintenanceRequest extends Partial<CreateMaintenanceRequest> {
  status?: 'OPEN' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'
  assignedTo?: string
  actualCost?: number
  completedDate?: string
  notes?: string
}

export interface MaintenanceFilters {
  status?: string
  category?: string
  priority?: string
  propertyId?: string
  tenantId?: string
  assignedTo?: string
}

export interface MaintenanceContractor {
  id: string
  name: string
  email: string
  phone: string
  specialties: string[]
  rating: number
  isActive: boolean
}

// Maintenance Service
const maintenanceService = {
  /**
   * Get all maintenance requests with pagination and filters
   */
  async getMaintenanceRequests(page: number = 1, limit: number = 10, filters?: MaintenanceFilters): Promise<ApiResponse<PaginatedResponse<MaintenanceRequest>>> {
    const params: Record<string, string> = {
      page: page.toString(),
      limit: limit.toString(),
    }

    if (filters?.status) params.status = filters.status
    if (filters?.category) params.category = filters.category
    if (filters?.priority) params.priority = filters.priority
    if (filters?.propertyId) params.propertyId = filters.propertyId
    if (filters?.tenantId) params.tenantId = filters.tenantId
    if (filters?.assignedTo) params.assignedTo = filters.assignedTo

    return api.get<PaginatedResponse<MaintenanceRequest>>('/maintenance', { params })
  },

  /**
   * Get a maintenance request by ID
   */
  async getMaintenanceRequestById(id: string): Promise<ApiResponse<MaintenanceRequest>> {
    return api.get<MaintenanceRequest>(`/maintenance/${id}`)
  },

  /**
   * Create a new maintenance request
   */
  async createMaintenanceRequest(data: CreateMaintenanceRequest): Promise<ApiResponse<MaintenanceRequest>> {
    if (data.images && data.images.length > 0) {
      const formData = new FormData()
      
      // Add all form fields
      Object.entries(data).forEach(([key, value]) => {
        if (key !== 'images' && value !== undefined) {
          formData.append(key, value.toString())
        }
      })
      
      // Add images
      data.images.forEach((image) => {
        formData.append('images', image)
      })

      return api.post<MaintenanceRequest>('/maintenance', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
    }

    return api.post<MaintenanceRequest>('/maintenance', data)
  },

  /**
   * Update a maintenance request
   */
  async updateMaintenanceRequest(id: string, data: UpdateMaintenanceRequest): Promise<ApiResponse<MaintenanceRequest>> {
    return api.put<MaintenanceRequest>(`/maintenance/${id}`, data)
  },

  /**
   * Delete a maintenance request
   */
  async deleteMaintenanceRequest(id: string): Promise<ApiResponse<void>> {
    return api.delete<void>(`/maintenance/${id}`)
  },

  /**
   * Assign maintenance request to contractor
   */
  async assignMaintenanceRequest(id: string, contractorId: string): Promise<ApiResponse<MaintenanceRequest>> {
    return api.put<MaintenanceRequest>(`/maintenance/${id}/assign`, { contractorId })
  },

  /**
   * Update maintenance request status
   */
  async updateMaintenanceStatus(id: string, status: 'OPEN' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'): Promise<ApiResponse<MaintenanceRequest>> {
    return api.put<MaintenanceRequest>(`/maintenance/${id}/status`, { status })
  },

  /**
   * Get emergency maintenance requests
   */
  async getEmergencyRequests(): Promise<ApiResponse<MaintenanceRequest[]>> {
    return api.get<MaintenanceRequest[]>('/maintenance/emergency')
  },

  /**
   * Get maintenance contractors
   */
  async getContractors(): Promise<ApiResponse<MaintenanceContractor[]>> {
    return api.get<MaintenanceContractor[]>('/maintenance/contractors')
  },

  /**
   * Upload additional images to maintenance request
   */
  async uploadMaintenanceImages(id: string, images: File[]): Promise<ApiResponse<{ imageUrls: string[] }>> {
    const formData = new FormData()
    images.forEach((image) => {
      formData.append('images', image)
    })

    return api.post<{ imageUrls: string[] }>(`/maintenance/${id}/images`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  /**
   * Get maintenance statistics
   */
  async getMaintenanceStats(): Promise<ApiResponse<any>> {
    return api.get<any>('/maintenance/stats')
  }
}

export default maintenanceService
