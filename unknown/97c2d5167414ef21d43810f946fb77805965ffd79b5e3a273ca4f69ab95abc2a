/**
 * Leases Service
 * 
 * Centralized service for all lease-related API calls
 */

import { api } from '../api-instance'
import type { ApiResponse, PaginatedResponse } from '@/lib/types'

// Lease Types
export interface Lease {
  id: string
  propertyId: string
  tenantId: string
  startDate: string
  endDate: string
  monthlyRent: number
  securityDeposit: number
  status: 'ACTIVE' | 'EXPIRED' | 'TERMINATED' | 'PENDING'
  terms: {
    petPolicy: boolean
    smokingPolicy: boolean
    sublettingAllowed: boolean
    earlyTerminationFee?: number
    lateFeeAmount?: number
    lateFeeGracePeriod?: number
  }
  documents: {
    leaseAgreement?: string
    moveInChecklist?: string
    moveOutChecklist?: string
  }
  createdAt: string
  updatedAt: string
  property?: {
    id: string
    title: string
    address: string
  }
  tenant?: {
    id: string
    firstName: string
    lastName: string
    email: string
    phone: string
  }
}

export interface CreateLeaseRequest {
  propertyId: string
  tenantId: string
  startDate: string
  endDate: string
  monthlyRent: number
  securityDeposit: number
  terms: {
    petPolicy: boolean
    smokingPolicy: boolean
    sublettingAllowed: boolean
    earlyTerminationFee?: number
    lateFeeAmount?: number
    lateFeeGracePeriod?: number
  }
}

export interface UpdateLeaseRequest extends Partial<CreateLeaseRequest> {
  status?: 'ACTIVE' | 'EXPIRED' | 'TERMINATED' | 'PENDING'
}

export interface LeaseFilters {
  status?: string
  propertyId?: string
  tenantId?: string
  startDate?: string
  endDate?: string
}

// Leases Service
const leasesService = {
  /**
   * Get all leases with pagination and filters
   */
  async getLeases(page: number = 1, limit: number = 10, filters?: LeaseFilters): Promise<ApiResponse<PaginatedResponse<Lease>>> {
    const params: Record<string, string> = {
      page: page.toString(),
      limit: limit.toString(),
    }

    if (filters?.status) params.status = filters.status
    if (filters?.propertyId) params.propertyId = filters.propertyId
    if (filters?.tenantId) params.tenantId = filters.tenantId
    if (filters?.startDate) params.startDate = filters.startDate
    if (filters?.endDate) params.endDate = filters.endDate

    return api.get<PaginatedResponse<Lease>>('/leases', { params })
  },

  /**
   * Get a lease by ID
   */
  async getLeaseById(id: string): Promise<ApiResponse<Lease>> {
    return api.get<Lease>(`/leases/${id}`)
  },

  /**
   * Create a new lease
   */
  async createLease(data: CreateLeaseRequest): Promise<ApiResponse<Lease>> {
    return api.post<Lease>('/leases', data)
  },

  /**
   * Update a lease
   */
  async updateLease(id: string, data: UpdateLeaseRequest): Promise<ApiResponse<Lease>> {
    return api.put<Lease>(`/leases/${id}`, data)
  },

  /**
   * Delete a lease
   */
  async deleteLease(id: string): Promise<ApiResponse<void>> {
    return api.delete<void>(`/leases/${id}`)
  },

  /**
   * Terminate a lease
   */
  async terminateLease(id: string, terminationDate: string, reason?: string): Promise<ApiResponse<Lease>> {
    return api.put<Lease>(`/leases/${id}/terminate`, { terminationDate, reason })
  },

  /**
   * Renew a lease
   */
  async renewLease(id: string, newEndDate: string, newRent?: number): Promise<ApiResponse<Lease>> {
    return api.put<Lease>(`/leases/${id}/renew`, { newEndDate, newRent })
  },

  /**
   * Get leases expiring soon
   */
  async getExpiringLeases(days: number = 30): Promise<ApiResponse<Lease[]>> {
    return api.get<Lease[]>('/leases/expiring', { params: { days: days.toString() } })
  },

  /**
   * Upload lease document
   */
  async uploadLeaseDocument(id: string, documentType: string, file: File): Promise<ApiResponse<any>> {
    const formData = new FormData()
    formData.append('document', file)
    formData.append('type', documentType)

    return api.post(`/leases/${id}/documents`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  /**
   * Get lease documents
   */
  async getLeaseDocuments(id: string): Promise<ApiResponse<any[]>> {
    return api.get<any[]>(`/leases/${id}/documents`)
  }
}

export default leasesService
