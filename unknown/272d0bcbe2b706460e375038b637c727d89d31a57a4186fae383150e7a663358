"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Logo } from "@/components/logo"
import Link from "next/link"
import { ArrowLeft, AtSign, Building, Check, Eye, EyeOff, Lock, User } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import { useRouter } from "next/navigation"
import env, { getApiUrl } from "@/lib/env"
import { signupSchema } from "@/lib/validations"
import type { AccountType, Language, BusinessType, SignupData } from "@/lib/types"

type FormData = z.infer<typeof signupSchema>

export default function SignupPage() {
  const [accountType, setAccountType] = useState<AccountType>("individual")
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()
  const router = useRouter()

  const form = useForm<FormData>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      accountType: "individual",
      email: "",
      preferredLang: false,
      password: "",
      confirmPassword: "",
      firstName: "",
      lastName: "",
    },
  })

  async function onSubmit(data: FormData) {
    setIsSubmitting(true)
    
    try {
      // Convert the form data to the format expected by the backend
      const backendData = {
        businessType: data.accountType === "individual" ? "INDIVIDUAL" as BusinessType : "COMPANY" as BusinessType,
        businessName: data.accountType === "company" ? data.companyName : null,
        firstName: data.accountType === "individual" ? data.firstName : null,
        lastName: data.accountType === "individual" ? data.lastName : null,
        preferredLanguage: data.preferredLang ? "fr" as Language : "en" as Language,
        email: data.email,
        password: data.password
      }
      
      console.log(`Environment: ${env.ENVIRONMENT}, Using API URL: ${env.API_URL}`)
      
      // Send POST request to the backend using environment-based URL
      const response = await fetch(getApiUrl('/signup/managers'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(backendData),
      })
      
      if (!response.ok) {
        // If the response is not successful, parse the error and throw it
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to create account')
      }
      
      // Success - redirect to confirmation page
      router.push('/signup/confirmation')
    } catch (error) {
      // Handle errors
      console.error('Signup error:', error)
      toast({
        title: "Error creating account",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }
  
  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  }

  return (
    <main className="min-h-screen gradient-bg flex items-center justify-center p-4">
      {!env.isProd && (
        <div className={`absolute top-0 left-0 right-0 text-black text-xs text-center py-1 ${
          env.isStaging ? 'bg-orange-400' : 'bg-amber-500'
        }`}>
          {env.ENVIRONMENT.toUpperCase()} ENVIRONMENT - API: {env.API_URL}
        </div>
      )}
      <div className="absolute top-4 left-4 z-10">
        <Button variant="ghost" size="icon" asChild className="text-white/80 hover:text-white hover:bg-white/10 transition-all">
          <Link href="/">
            <ArrowLeft className="h-5 w-5" />
          </Link>
        </Button>
      </div>
      
      <div className="absolute top-4 right-4 z-10">
        <Logo className="scale-75" />
      </div>
      
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md mx-auto"
      >
        <Card className="bg-secondary/20 backdrop-blur-md border-white/10 shadow-2xl overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary via-primary/50 to-transparent"></div>
          
          <CardHeader className="space-y-4 pb-6">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <CardTitle className="text-3xl font-bold text-center bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                Create Your Account
              </CardTitle>
              <CardDescription className="text-center text-white/60 mt-2">
                Join Vestral Managers and start managing your properties
              </CardDescription>
            </motion.div>
          </CardHeader>
          
          <CardContent>
            <Form {...form}>
              <motion.form 
                onSubmit={form.handleSubmit(onSubmit)} 
                className="space-y-6"
                variants={container}
                initial="hidden"
                animate="show"
              >
                <motion.div variants={item}>
                  <FormField
                    control={form.control}
                    name="accountType"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel className="text-white/80">Account Type</FormLabel>
                        <FormControl>
                          <div className="grid grid-cols-2 gap-4">
                            <div
                              className={cn(
                                "flex flex-col items-center justify-center p-4 rounded-lg border border-white/10 cursor-pointer transition-all",
                                field.value === "individual" 
                                  ? "bg-primary/20 border-primary" 
                                  : "bg-white/5 hover:bg-white/10"
                              )}
                              onClick={() => {
                                field.onChange("individual")
                                setAccountType("individual")
                                form.reset({
                                  ...form.getValues(),
                                  accountType: "individual",
                                  firstName: "",
                                  lastName: "",
                                  companyName: undefined,
                                })
                              }}
                            >
                              <User className={cn(
                                "h-6 w-6 mb-2",
                                field.value === "individual" ? "text-primary" : "text-white/60"
                              )} />
                              <span className={cn(
                                "text-sm font-medium",
                                field.value === "individual" ? "text-white" : "text-white/60"
                              )}>Individual</span>
                              {field.value === "individual" && (
                                <div className="absolute top-2 right-2">
                                  <Check className="h-4 w-4 text-primary" />
                                </div>
                              )}
                            </div>
                            <div
                              className={cn(
                                "flex flex-col items-center justify-center p-4 rounded-lg border border-white/10 cursor-pointer transition-all",
                                field.value === "company" 
                                  ? "bg-primary/20 border-primary" 
                                  : "bg-white/5 hover:bg-white/10"
                              )}
                              onClick={() => {
                                field.onChange("company")
                                setAccountType("company")
                                form.reset({
                                  ...form.getValues(),
                                  accountType: "company",
                                  firstName: undefined,
                                  lastName: undefined,
                                  companyName: "",
                                })
                              }}
                            >
                              <Building className={cn(
                                "h-6 w-6 mb-2",
                                field.value === "company" ? "text-primary" : "text-white/60"
                              )} />
                              <span className={cn(
                                "text-sm font-medium",
                                field.value === "company" ? "text-white" : "text-white/60"
                              )}>Company</span>
                              {field.value === "company" && (
                                <div className="absolute top-2 right-2">
                                  <Check className="h-4 w-4 text-primary" />
                                </div>
                              )}
                            </div>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>

                <motion.div variants={item}>
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white/80">Email</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <AtSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-4 w-4" />
                            <Input 
                              placeholder="<EMAIL>" 
                              {...field}
                              className="bg-white/5 border-white/10 text-white placeholder:text-white/40 pl-10 focus:border-primary focus:ring-primary/20"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>

                {accountType === "individual" ? (
                  <motion.div variants={item} className="grid gap-6 sm:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-white/80">First Name</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-4 w-4" />
                              <Input 
                                placeholder="John" 
                                {...field}
                                className="bg-white/5 border-white/10 text-white placeholder:text-white/40 pl-10 focus:border-primary focus:ring-primary/20"
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-white/80">Last Name</FormLabel>
                          <FormControl>
                            <Input 
                              placeholder="Doe" 
                              {...field}
                              className="bg-white/5 border-white/10 text-white placeholder:text-white/40 focus:border-primary focus:ring-primary/20"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </motion.div>
                ) : (
                  <motion.div variants={item}>
                    <FormField
                      control={form.control}
                      name="companyName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-white/80">Company Name</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-4 w-4" />
                              <Input 
                                placeholder="Acme Inc." 
                                {...field}
                                className="bg-white/5 border-white/10 text-white placeholder:text-white/40 pl-10 focus:border-primary focus:ring-primary/20"
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </motion.div>
                )}

                <motion.div variants={item}>
                  <FormField
                    control={form.control}
                    name="preferredLang"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-white/10 bg-white/5 p-4 hover:bg-white/10 transition-colors">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base text-white/80">
                            Preferred Language
                          </FormLabel>
                          <div className="text-sm text-white/60">
                            {field.value ? "Français" : "English"}
                          </div>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-primary"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </motion.div>

                <motion.div variants={item} className="grid gap-6 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white/80">Password</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-4 w-4" />
                            <Input 
                              type={showPassword ? "text" : "password"} 
                              {...field}
                              className="bg-white/5 border-white/10 text-white pl-10 pr-10 focus:border-primary focus:ring-primary/20"
                            />
                            <button 
                              type="button"
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/40 hover:text-white/60"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white/80">Confirm Password</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-4 w-4" />
                            <Input 
                              type={showConfirmPassword ? "text" : "password"} 
                              {...field}
                              className="bg-white/5 border-white/10 text-white pl-10 pr-10 focus:border-primary focus:ring-primary/20"
                            />
                            <button 
                              type="button"
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/40 hover:text-white/60"
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            >
                              {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>

                <motion.div variants={item}>
                  <Button 
                    type="submit" 
                    className="w-full bg-primary hover:bg-primary/90 text-white font-medium h-12 transition-all"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <span className="animate-spin mr-2">
                          <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        </span>
                        Creating Account...
                      </>
                    ) : (
                      "Create Account"
                    )}
                  </Button>
                </motion.div>
              </motion.form>
            </Form>
          </CardContent>
          
          <CardFooter className="flex justify-center pb-8">
            <motion.p 
              className="text-sm text-white/60"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              Already have an account?{" "}
              <Link href="/login" className="text-primary hover:text-primary/90 hover:underline transition-colors">
                Sign in
              </Link>
            </motion.p>
          </CardFooter>
        </Card>
      </motion.div>
    </main>
  )
}