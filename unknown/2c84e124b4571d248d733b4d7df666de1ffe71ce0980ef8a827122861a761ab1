import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { exchangeCodeForTokens } from "@/lib/auth.server";
import env from "@/lib/env";

/**
 * Get the base URL for the current environment
 * This is a server-side function so we can't use window.location
 */
function getBaseUrl(request: NextRequest): string {
  // For development, we'll extract the host and port from the request
  if (env.isDev) {
    const host = request.headers.get('host') || 'localhost:3000';
    return `http://${host}`;
  }
  
  // For production and staging, use the configured base URL
  return env.BASE_URL;
}

/**
 * OAuth callback handler for AWS Cognito
 * This is where users are redirected after authenticating with Cognito
 */
export async function GET(request: NextRequest) {
  try {
    // Get the base URL for the current environment
    const baseUrl = getBaseUrl(request);
    console.log("Callback received, base URL:", baseUrl);
    
    // Get the authorization code from the query parameters
    const { searchParams } = new URL(request.url);
    const code = searchParams.get("code");
    
    if (!code) {
      console.error("No code provided in callback");
      return NextResponse.redirect(new URL("/login?error=no_code", baseUrl));
    }
    
    // Exchange the code for tokens
    const tokens = await exchangeCodeForTokens(code, request);
    
    if (!tokens || !tokens.access_token) {
      console.error("Failed to exchange code for tokens");
      return NextResponse.redirect(new URL("/login?error=auth_failed", baseUrl));
    }
    
    // Set the tokens in cookies
    const cookieStore = cookies();
    
    // Set session cookie with the access token
    // In production, you'd want to set more secure cookie options
    cookieStore.set("session", tokens.access_token, {
      httpOnly: true,
      path: "/",
      maxAge: 60 * 60 * 24, // 1 day
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
    });
    
    // If there's a refresh token, store it as well
    if (tokens.refresh_token) {
      cookieStore.set("refresh_token", tokens.refresh_token, {
        httpOnly: true,
        path: "/",
        maxAge: 60 * 60 * 24 * 30, // 30 days
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
      });
    }
    
    // Redirect to the dashboard after successful authentication
    const redirectUrl = `${baseUrl}/dashboard`;
    
    console.log("Redirecting after auth to:", redirectUrl);
    
    return NextResponse.redirect(redirectUrl);
  } catch (error) {
    console.error("Error in auth callback:", error);
    const baseUrl = getBaseUrl(request);
    return NextResponse.redirect(new URL("/login?error=server_error", baseUrl));
  }
}