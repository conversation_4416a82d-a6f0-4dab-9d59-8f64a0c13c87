import { <PERSON><PERSON> } from "@/components/ui/button"
import { Logo } from "@/components/shared/logo"
import Link from "next/link"
import { ArrowRight, LogIn, Search, Building2 } from "lucide-react"

export default function Home() {
  return (
    <main className="min-h-screen gradient-bg">
      <div className="container mx-auto px-4 py-8">
        {/* Navigation */}
        <nav className="flex justify-between items-center mb-16">
          <Logo />
          <div className="flex items-center gap-4">
            <Button variant="ghost" className="text-white gap-2" asChild>
              <Link href="/properties">
                <Search className="h-4 w-4" />
                Browse Properties
              </Link>
            </Button>
            <Button variant="ghost" className="text-white gap-2" asChild>
              <Link href="/login">
                <LogIn className="h-4 w-4" />
                Sign In
              </Link>
            </Button>
          </div>
        </nav>

        {/* Hero Section */}
        <div className="flex flex-col items-center justify-center space-y-8 text-center max-w-3xl mx-auto pt-16">
          <h1 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
            Streamline Your Business Management
          </h1>
          <p className="text-lg text-white/60 max-w-2xl">
            Welcome to Vestral Managers, your comprehensive solution for modern business management.
            Enhance productivity and make data-driven decisions with our powerful tools.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
            <Button size="lg" className="gap-2 text-lg h-12 px-8" asChild>
              <Link href="/signup">
                Get Started
                <ArrowRight className="h-5 w-5" />
              </Link>
            </Button>
            <Button size="lg" variant="secondary" className="gap-2 text-lg h-12 px-8 bg-white/10 hover:bg-white/20" asChild>
              <Link href="/properties">
                <Building2 className="h-5 w-5" />
                Browse Properties
              </Link>
            </Button>
            <Button size="lg" variant="secondary" className="gap-2 text-lg h-12 px-8 bg-white/10 hover:bg-white/20" asChild>
              <Link href="/login">
                <LogIn className="h-5 w-5" />
                Sign In
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </main>
  )
}