/**
 * Property-related type definitions
 */

import { BaseEntity, Coordinates } from './common.types';

export type PropertyStatus = 'LIVE' | 'RENTED' | 'PAUSED';

export interface Property extends BaseEntity {
  title: string;
  address: string;
  price: number;
  status: PropertyStatus;
  city?: string;
  state?: string;
  zipCode?: string;
  propertyType?: string;
  units?: number;
  bedrooms?: number;
  bathrooms?: number;
  squareFeet?: number;
  description?: string;
  amenities?: string[];
  images?: string[];
  coordinates?: Coordinates;
  unitNumber?: string;
  isFreeNow?: boolean;
  startDate?: string;
  isPetFriendly?: boolean;
  isSmokingAllowed?: boolean;
  isFurnished?: boolean;
  isSemiFurnished?: boolean;
  isParkingAvailable?: boolean;
  requireIdVerification?: boolean;
  requireCreditScore?: boolean;
  requireTAL?: boolean;
  requireBackground?: boolean;
  requireReference?: boolean;
}

// Detailed property response from API (properties/{id})
export interface PropertyDetails {
  title: string;
  description: string;
  unitNumber: string;
  address: string;
  type: string;
  price: number;
  bedroomNumber: number;
  bathroomNumber: number;
  status: PropertyStatus;
  isPetFriendly: boolean;
  isSmokingAllowed: boolean;
  isFurnished: boolean;
  isSemiFurnished: boolean;
  isFreeNow: boolean;
  requiredVerifications: string[];
  startDate: string;
  token: string;
  images: string[];
  numberOfApplications: number;
  numberOfVisits: number;
}

export interface CreatePropertyRequest {
  name: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  propertyType: string;
  units?: number;
  bedrooms?: number;
  bathrooms?: number;
  squareFeet?: number;
  description?: string;
  amenities?: string[];
  images?: File[];
}

export interface PropertyStatusUpdateRequest {
  propertyId: string;
  status: string;
}

export interface PropertyFormValues {
  title: string;
  type: string;
  description: string;
  address: string;
  city: string;
  province: string;
  zip: string;
  unitNumber?: string;
  coordinates?: Coordinates;
  bedrooms: string;
  bathrooms: string;
  price: string;
  isFreeNow: boolean;
  startDate?: Date;
  isPetFriendly: boolean;
  isSmokingAllowed: boolean;
  isFurnished: boolean;
  isSemiFurnished: boolean;
  isParkingAvailable: boolean;
  requireIdVerification: boolean;
  requireCreditScore: boolean;
  requireTAL: boolean;
  requireBackground: boolean;
  requireReference: boolean;
}
