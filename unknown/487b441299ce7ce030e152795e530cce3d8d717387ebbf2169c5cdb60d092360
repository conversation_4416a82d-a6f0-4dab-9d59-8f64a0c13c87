import PropertyDetailsClient from "./property-details-client"
import { propertiesService } from "@/lib/services"

// Generate static params for static export (server component function)
export async function generateStaticParams() {
  try {
    // Fetch all properties to generate static params
    const response = await propertiesService.getProperties()

    if (response.data && Array.isArray(response.data)) {
      // Return all property IDs for static generation
      return response.data.map((property) => ({
        id: property.id || 'unknown',
      }))
    }

    // Fallback: return some common/placeholder IDs to prevent build errors
    return [
      { id: 'placeholder' },
      { id: 'sample' },
      { id: 'demo' }
    ]
  } catch (error) {
    console.warn('Failed to generate static params for properties:', error)
    // Return placeholder IDs to prevent build failures
    return [
      { id: 'placeholder' },
      { id: 'sample' },
      { id: 'demo' }
    ]
  }
}

// Main page component (server component)
export default function PropertyDetailsPage() {
  return <PropertyDetailsClient />
}
