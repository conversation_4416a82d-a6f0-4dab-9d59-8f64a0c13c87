/**
 * Property validation schemas
 */

import { z } from 'zod';
import { coordinatesSchema } from './common.validations';

export const propertyStatusSchema = z.enum(['LIVE', 'RENTED', 'PAUSED']);

export const propertySchema = z.object({
  title: z.string().min(1, 'Property title is required'),
  type: z.string().min(1, 'Property type is required'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  address: z.string().min(1, 'Address is required'),
  city: z.string().min(1, 'City is required'),
  province: z.string().min(1, 'Province is required'),
  zip: z.string().min(1, 'ZIP/Postal code is required'),
  unitNumber: z.string().optional(),
  coordinates: coordinatesSchema.optional(),
  bedrooms: z.string()
    .min(1, 'Number of bedrooms is required')
    .refine((val) => /^\d+$/.test(val), {
      message: 'Bedrooms must be a whole number'
    }),
  bathrooms: z.string()
    .min(1, 'Number of bathrooms is required')
    .refine((val) => /^\d+(\.\d+)?$/.test(val), {
      message: 'Bathrooms must be a valid number'
    }),
  price: z.string()
    .min(1, 'Price is required')
    .refine((val) => /^\d+(\.\d{1,2})?$/.test(val), {
      message: 'Price must be a valid amount'
    })
    .transform(val => parseFloat(val)),
  isFreeNow: z.boolean(),
  startDate: z.date().optional(),
  isPetFriendly: z.boolean(),
  isSmokingAllowed: z.boolean(),
  isFurnished: z.boolean(),
  isSemiFurnished: z.boolean(),
  isParkingAvailable: z.boolean(),
  requireIdVerification: z.boolean(),
  requireCreditScore: z.boolean(),
  requireTAL: z.boolean(),
  requireBackground: z.boolean(),
  requireReference: z.boolean(),
});

export const createPropertySchema = z.object({
  name: z.string().min(1, 'Property name is required'),
  address: z.string().min(1, 'Address is required'),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  zipCode: z.string().min(1, 'ZIP code is required'),
  propertyType: z.string().min(1, 'Property type is required'),
  units: z.number().min(1).optional(),
  bedrooms: z.number().min(0).optional(),
  bathrooms: z.number().min(0).optional(),
  squareFeet: z.number().min(1).optional(),
  description: z.string().optional(),
  amenities: z.array(z.string()).optional(),
});

export const updatePropertyStatusSchema = z.object({
  propertyId: z.string().min(1, 'Property ID is required'),
  status: propertyStatusSchema,
});
