"use client";

import { COGNITO_CONFIG } from "./auth-config";
import env from "./env";

/**
 * Get the Cognito authentication URL
 * @param useImplicitFlow Whether to use implicit flow (token response) instead of code flow
 * @returns The URL to redirect to for Cognito authentication
 */
export async function getAuthUrl(useImplicitFlow = true) {
  try {
    // Generate a random state value for CSRF protection
    const state = Math.random().toString(36).substring(2, 15);
    
    // Store state in session storage for validation when user returns
    sessionStorage.setItem("cognito_auth_state", state);
    
    // For implicit flow, redirect directly to dashboard
    // For code flow, redirect to callback API
    const redirectUri = useImplicitFlow 
      ? `${window.location.origin}/dashboard`
      : `${window.location.origin}/api/auth/callback`;
    
    // Build the authorization URL with required parameters
    const params = new URLSearchParams({
      client_id: COGNITO_CONFIG.CLIENT_ID,
      redirect_uri: redirectUri,
      response_type: useImplicitFlow ? "token" : "code",
      scope: "email openid profile",
      state,
    });

    // Use the exact Cognito URL format
    const authUrl = `https://${COGNITO_CONFIG.DOMAIN}/login?${params.toString()}`;
    
    // Log the auth configuration for debugging
    console.log("Auth configuration:", {
      clientId: COGNITO_CONFIG.CLIENT_ID,
      domain: COGNITO_CONFIG.DOMAIN,
      redirectUri,
      responseType: useImplicitFlow ? "token" : "code",
      fullAuthUrl: authUrl,
      isDev: env.isDev,
      currentPort: window.location.port,
      window_location: typeof window !== 'undefined' ? {
        protocol: window.location.protocol,
        host: window.location.host,
        hostname: window.location.hostname,
        port: window.location.port,
        origin: window.location.origin
      } : null
    });
    
    return authUrl;
  } catch (error) {
    console.error("Error generating auth URL:", error);
    throw error;
  }
}

/**
 * Get the Cognito logout URL
 * @returns The URL to redirect to for Cognito logout
 */
export async function getLogoutUrl() {
  // Use the DEV.getUrl() function which already handles the protocol correctly
  const redirectUri = env.isDev ? env.DEV.getUrl() : `${window.location.origin}`;
  console.log("Logout redirect URI:", redirectUri);
  return `https://${COGNITO_CONFIG.DOMAIN}/logout?client_id=${COGNITO_CONFIG.CLIENT_ID}&logout_uri=${redirectUri}`;
}

/**
 * Handle client-side logout
 * Clears session storage and redirects to the logout API
 */
export async function logout() {
  // Clear session storage
  sessionStorage.removeItem("authRedirect");
  sessionStorage.removeItem("cognito_auth_state");
  
  try {
    // Call the logout API
    await fetch("/api/auth/logout", {
      method: "POST",
    });
    
    // Redirect to home page
    window.location.href = "/";
  } catch (error) {
    console.error("Error during logout:", error);
    // Fallback to direct redirect
    window.location.href = "/";
  }
}

/**
 * Check if the user is authenticated client-side
 * This is a simple check and doesn't validate the token
 * For secure routes, use server-side validation
 */
export function isAuthenticated() {
  // In a real implementation, you might check for a token in localStorage
  // or use a more sophisticated client-side auth state management
  return !!sessionStorage.getItem("isAuthenticated");
}