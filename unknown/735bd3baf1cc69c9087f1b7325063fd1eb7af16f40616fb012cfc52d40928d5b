/**
 * Payments Service
 * 
 * Centralized service for all payment-related API calls
 */

import { api } from '../api-instance'
import type { ApiResponse, PaginatedResponse } from '@/lib/types'

// Payment Types
export interface Payment {
  id: string
  leaseId: string
  tenantId: string
  propertyId: string
  amount: number
  type: 'RENT' | 'SECURITY_DEPOSIT' | 'LATE_FEE' | 'MAINTENANCE' | 'OTHER'
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED'
  paymentMethod: 'CREDIT_CARD' | 'BANK_TRANSFER' | 'CHECK' | 'CASH' | 'OTHER'
  dueDate: string
  paidDate?: string
  description?: string
  transactionId?: string
  receiptUrl?: string
  createdAt: string
  updatedAt: string
  lease?: {
    id: string
    propertyTitle: string
    tenantName: string
  }
}

export interface CreatePaymentRequest {
  leaseId: string
  amount: number
  type: 'RENT' | 'SECURITY_DEPOSIT' | 'LATE_FEE' | 'MAINTENANCE' | 'OTHER'
  dueDate: string
  description?: string
}

export interface ProcessPaymentRequest {
  paymentId: string
  paymentMethod: 'CREDIT_CARD' | 'BANK_TRANSFER' | 'CHECK' | 'CASH' | 'OTHER'
  transactionId?: string
  paidDate?: string
}

export interface PaymentFilters {
  status?: string
  type?: string
  leaseId?: string
  tenantId?: string
  propertyId?: string
  startDate?: string
  endDate?: string
}

export interface PaymentSummary {
  totalCollected: number
  totalPending: number
  totalOverdue: number
  monthlyRevenue: number
  collectionRate: number
}

// Payments Service
const paymentsService = {
  /**
   * Get all payments with pagination and filters
   */
  async getPayments(page: number = 1, limit: number = 10, filters?: PaymentFilters): Promise<ApiResponse<PaginatedResponse<Payment>>> {
    const params: Record<string, string> = {
      page: page.toString(),
      limit: limit.toString(),
    }

    if (filters?.status) params.status = filters.status
    if (filters?.type) params.type = filters.type
    if (filters?.leaseId) params.leaseId = filters.leaseId
    if (filters?.tenantId) params.tenantId = filters.tenantId
    if (filters?.propertyId) params.propertyId = filters.propertyId
    if (filters?.startDate) params.startDate = filters.startDate
    if (filters?.endDate) params.endDate = filters.endDate

    return api.get<PaginatedResponse<Payment>>('/payments', { params })
  },

  /**
   * Get a payment by ID
   */
  async getPaymentById(id: string): Promise<ApiResponse<Payment>> {
    return api.get<Payment>(`/payments/${id}`)
  },

  /**
   * Create a new payment
   */
  async createPayment(data: CreatePaymentRequest): Promise<ApiResponse<Payment>> {
    return api.post<Payment>('/payments', data)
  },

  /**
   * Process a payment
   */
  async processPayment(data: ProcessPaymentRequest): Promise<ApiResponse<Payment>> {
    return api.put<Payment>(`/payments/${data.paymentId}/process`, data)
  },

  /**
   * Mark payment as failed
   */
  async markPaymentFailed(id: string, reason?: string): Promise<ApiResponse<Payment>> {
    return api.put<Payment>(`/payments/${id}/failed`, { reason })
  },

  /**
   * Refund a payment
   */
  async refundPayment(id: string, amount?: number, reason?: string): Promise<ApiResponse<Payment>> {
    return api.put<Payment>(`/payments/${id}/refund`, { amount, reason })
  },

  /**
   * Get overdue payments
   */
  async getOverduePayments(): Promise<ApiResponse<Payment[]>> {
    return api.get<Payment[]>('/payments/overdue')
  },

  /**
   * Get payment summary/statistics
   */
  async getPaymentSummary(startDate?: string, endDate?: string): Promise<ApiResponse<PaymentSummary>> {
    const params: Record<string, string> = {}
    if (startDate) params.startDate = startDate
    if (endDate) params.endDate = endDate

    return api.get<PaymentSummary>('/payments/summary', { params })
  },

  /**
   * Generate payment receipt
   */
  async generateReceipt(id: string): Promise<ApiResponse<{ receiptUrl: string }>> {
    return api.post<{ receiptUrl: string }>(`/payments/${id}/receipt`)
  },

  /**
   * Send payment reminder
   */
  async sendPaymentReminder(id: string): Promise<ApiResponse<void>> {
    return api.post<void>(`/payments/${id}/reminder`)
  },

  /**
   * Bulk create recurring payments (monthly rent)
   */
  async createRecurringPayments(leaseId: string, months: number): Promise<ApiResponse<Payment[]>> {
    return api.post<Payment[]>('/payments/recurring', { leaseId, months })
  },

  /**
   * Get payment history for a tenant
   */
  async getTenantPaymentHistory(tenantId: string): Promise<ApiResponse<Payment[]>> {
    return api.get<Payment[]>(`/payments/tenant/${tenantId}`)
  },

  /**
   * Get payment history for a property
   */
  async getPropertyPaymentHistory(propertyId: string): Promise<ApiResponse<Payment[]>> {
    return api.get<Payment[]>(`/payments/property/${propertyId}`)
  }
}

export default paymentsService
