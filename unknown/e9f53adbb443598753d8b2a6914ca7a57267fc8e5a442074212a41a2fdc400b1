"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";

/**
 * Component to handle implicit flow authentication
 * Extracts tokens from URL hash fragment and stores them in cookies
 */
export function AuthHandler() {
  const router = useRouter();

  useEffect(() => {
    // Function to parse hash fragment
    const parseHash = () => {
      if (typeof window === "undefined") return;

      console.log("AuthHandler: Checking for tokens in URL");
      console.log("Current URL:", window.location.href);
      console.log("Hash fragment:", window.location.hash);

      // Get the hash fragment (everything after #)
      const hash = window.location.hash.substring(1);

      if (!hash) {
        console.log("AuthHandler: No hash fragment found");
        return;
      }

      console.log("AuthHandler: Hash fragment found:", hash);

      // Parse the hash fragment into an object
      const params = hash.split("&").reduce((result: Record<string, string>, item) => {
        const parts = item.split("=");
        if (parts.length === 2) {
          result[parts[0]] = decodeURIComponent(parts[1]);
        }
        return result;
      }, {});

      console.log("AuthHandler: Parsed params:", params);

      // Extract tokens
      const accessToken = params.access_token;
      const idToken = params.id_token;
      const expiresIn = params.expires_in ? parseInt(params.expires_in) : 3600;

      if (accessToken) {
        console.log("AuthHandler: Access token found, storing in cookies");
        console.log("AuthHandler: Token expires in:", expiresIn, "seconds");

        // Store tokens in cookies
        Cookies.set("session", accessToken, {
          path: "/",
          secure: process.env.NODE_ENV === "production",
          sameSite: "lax",
          expires: new Date(new Date().getTime() + expiresIn * 1000)
        });

        if (idToken) {
          console.log("AuthHandler: ID token found, storing in cookies");
          Cookies.set("id_token", idToken, {
            path: "/",
            secure: process.env.NODE_ENV === "production",
            sameSite: "lax",
            expires: new Date(new Date().getTime() + expiresIn * 1000)
          });
        }

        // Verify cookies were set
        console.log("AuthHandler: Cookies after setting:");
        console.log("- session:", Cookies.get("session") ? "SET" : "NOT SET");
        console.log("- id_token:", Cookies.get("id_token") ? "SET" : "NOT SET");

        // Clear the hash fragment from the URL
        window.history.replaceState(
          {},
          document.title,
          window.location.pathname + window.location.search
        );

        console.log("AuthHandler: Hash fragment cleared from URL");
      } else {
        console.log("AuthHandler: No access token found in hash fragment");
      }
    };

    // Parse hash on component mount
    parseHash();

    // Also check for existing cookies
    console.log("AuthHandler: Checking existing cookies:");
    console.log("- session:", Cookies.get("session") ? "EXISTS" : "NOT FOUND");
    console.log("- id_token:", Cookies.get("id_token") ? "EXISTS" : "NOT FOUND");
  }, []);

  return null; // This component doesn't render anything
}
