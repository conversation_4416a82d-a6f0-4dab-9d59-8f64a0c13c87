/**
 * API-related type definitions
 */

export interface RequestOptions {
  headers?: HeadersInit;
  params?: Record<string, string>;
  signal?: AbortSignal;
}

export interface UseApiState<T> {
  data: T | null;
  error: string | null;
  isLoading: boolean;
}

export interface UseApiResponse<T> extends UseApiState<T> {
  execute: () => Promise<void>;
  reset: () => void;
}

export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

export interface ApiServiceConfig {
  baseUrl: string;
  defaultHeaders?: HeadersInit;
  timeout?: number;
}
