import { useState, useCallback } from "react"
import { api } from "../api-instance"
import type { ApiResponse, UseApiState, UseApiResponse, HttpMethod } from "@/lib/types"

export function useApi<T>(
  method: HttpMethod,
  endpoint: string,
  body?: any
): UseApiResponse<T> {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    error: null,
    isLoading: false,
  })

  const reset = useCallback(() => {
    setState({
      data: null,
      error: null,
      isLoading: false,
    })
  }, [])

  const execute = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true }))

    try {
      let response: ApiResponse<T>

      switch (method) {
        case "GET":
          response = await api.get<T>(endpoint)
          break
        case "POST":
          response = await api.post<T>(endpoint, body)
          break
        case "PUT":
          response = await api.put<T>(endpoint, body)
          break
        case "PATCH":
          response = await api.patch<T>(endpoint, body)
          break
        case "DELETE":
          response = await api.delete<T>(endpoint)
          break
        default:
          throw new Error(`Unsupported method: ${method}`)
      }

      setState({
        data: response.data,
        error: response.error || null,
        isLoading: false,
      })
    } catch (error) {
      setState({
        data: null,
        error: error instanceof Error ? error.message : "An error occurred",
        isLoading: false,
      })
    }
  }, [method, endpoint, body])

  return {
    ...state,
    execute,
    reset,
  }
}