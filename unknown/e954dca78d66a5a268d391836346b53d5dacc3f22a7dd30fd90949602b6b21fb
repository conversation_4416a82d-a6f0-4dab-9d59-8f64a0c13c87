import { api } from '../api-instance';
import { getApiUrl } from '../env';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phoneNumber?: string;
}

export interface ResendConfirmationEmailRequest {
  email: string;
}

export interface ResendConfirmationEmailResponse {
  success: boolean;
  message: string;
}

/**
 * Auth service for authentication-related API calls
 */
export const authService = {
  /**
   * Log in a user
   * @param data Login credentials
   */
  async login(data: LoginRequest) {
    return api.post('/auth/login', data);
  },

  /**
   * Sign up a new manager
   * @param data Signup information
   */
  async signup(data: SignupRequest) {
    return api.post('/signup/managers', data);
  },

  /**
   * Log out the current user
   */
  async logout() {
    return api.post('/auth/logout', {});
  },

  /**
   * Resend confirmation email to the user
   * @param email User email
   */
  async resendConfirmationEmail(email: string): Promise<ResendConfirmationEmailResponse> {
    return api.post('/auth/resend-confirmation', { email });
  },

  /**
   * Get the current user's session
   */
  async getSession() {
    return api.get('/auth/session');
  }
};

export default authService;
