import { getApiUrl } from "../env";

export interface ResendConfirmationEmailRequest {
  email: string;
}

export interface ResendConfirmationEmailResponse {
  success: boolean;
  message: string;
}

/**
 * Auth service for authentication-related API calls
 */
export const authService = {
  /**
   * Resend confirmation email to the user
   * @param email User email
   * @returns API response
   */
  async resendConfirmationEmail(email: string): Promise<ResendConfirmationEmailResponse> {
    try {
      const response = await fetch(getApiUrl('/auth/resend-confirmation'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to resend confirmation email');
      }

      return await response.json();
    } catch (error) {
      console.error('Error resending confirmation email:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'An unexpected error occurred',
      };
    }
  },
};

export default authService;
