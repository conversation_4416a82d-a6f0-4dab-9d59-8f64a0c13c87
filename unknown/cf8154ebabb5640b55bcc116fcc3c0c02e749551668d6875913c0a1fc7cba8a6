/**
 * Tenants Service
 * 
 * Centralized service for all tenant-related API calls
 */

import { api } from '../api-instance'
import type { ApiResponse, PaginatedResponse } from '@/lib/types'

// Tenant Types
export interface Tenant {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  dateOfBirth?: string
  emergencyContact?: {
    name: string
    phone: string
    relationship: string
  }
  employmentInfo?: {
    employer: string
    position: string
    monthlyIncome: number
  }
  currentAddress?: {
    street: string
    city: string
    state: string
    zipCode: string
  }
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'BLACKLISTED'
  createdAt: string
  updatedAt: string
}

export interface CreateTenantRequest {
  firstName: string
  lastName: string
  email: string
  phone: string
  dateOfBirth?: string
  emergencyContact?: {
    name: string
    phone: string
    relationship: string
  }
  employmentInfo?: {
    employer: string
    position: string
    monthlyIncome: number
  }
  currentAddress?: {
    street: string
    city: string
    state: string
    zipCode: string
  }
}

export interface UpdateTenantRequest extends Partial<CreateTenantRequest> {
  status?: 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'BLACKLISTED'
}

export interface TenantFilters {
  status?: string
  search?: string
  propertyId?: string
}

// Tenants Service
const tenantsService = {
  /**
   * Get all tenants with pagination and filters
   */
  async getTenants(page: number = 1, limit: number = 10, filters?: TenantFilters): Promise<ApiResponse<PaginatedResponse<Tenant>>> {
    const params: Record<string, string> = {
      page: page.toString(),
      limit: limit.toString(),
    }

    if (filters?.status) params.status = filters.status
    if (filters?.search) params.search = filters.search
    if (filters?.propertyId) params.propertyId = filters.propertyId

    return api.get<PaginatedResponse<Tenant>>('/tenants', { params })
  },

  /**
   * Get a tenant by ID
   */
  async getTenantById(id: string): Promise<ApiResponse<Tenant>> {
    return api.get<Tenant>(`/tenants/${id}`)
  },

  /**
   * Create a new tenant
   */
  async createTenant(data: CreateTenantRequest): Promise<ApiResponse<Tenant>> {
    return api.post<Tenant>('/tenants', data)
  },

  /**
   * Update a tenant
   */
  async updateTenant(id: string, data: UpdateTenantRequest): Promise<ApiResponse<Tenant>> {
    return api.put<Tenant>(`/tenants/${id}`, data)
  },

  /**
   * Delete a tenant
   */
  async deleteTenant(id: string): Promise<ApiResponse<void>> {
    return api.delete<void>(`/tenants/${id}`)
  },

  /**
   * Get tenant's rental history
   */
  async getTenantRentalHistory(id: string): Promise<ApiResponse<any[]>> {
    return api.get<any[]>(`/tenants/${id}/rental-history`)
  },

  /**
   * Get tenant's payment history
   */
  async getTenantPaymentHistory(id: string): Promise<ApiResponse<any[]>> {
    return api.get<any[]>(`/tenants/${id}/payment-history`)
  },

  /**
   * Update tenant status
   */
  async updateTenantStatus(id: string, status: 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'BLACKLISTED'): Promise<ApiResponse<Tenant>> {
    return api.put<Tenant>(`/tenants/${id}/status`, { status })
  },

  /**
   * Search tenants
   */
  async searchTenants(query: string): Promise<ApiResponse<Tenant[]>> {
    return api.get<Tenant[]>('/tenants/search', { params: { q: query } })
  }
}

export default tenantsService
