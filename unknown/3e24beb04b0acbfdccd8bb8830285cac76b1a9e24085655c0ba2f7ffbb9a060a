import { api } from '../api-instance';
import Cookies from 'js-cookie';
import env from '../env';
import type {
  Property,
  PropertyDetails,
  PropertyStatus,
  CreatePropertyRequest,
  PropertyStatusUpdateRequest
} from '@/lib/types';

/**
 * Helper function to upload property with FormData (multipart/form-data)
 * This follows the API contract: propertyDto (string) + images (array)
 */
async function uploadPropertyWithFormData(endpoint: string, formData: FormData) {
  // Get authentication token
  const idToken = Cookies.get('id_token');
  const accessToken = Cookies.get('access_token');
  const token = idToken || accessToken;

  const response = await fetch(`${env.API_URL}${endpoint}`, {
    method: 'POST',
    headers: {
      ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
      // Don't set Content-Type - browser will set it with boundary for multipart/form-data
    },
    body: formData,
    credentials: 'include',
  });

  // Handle response similar to ApiService
  const status = response.status;

  try {
    // Check if response has content
    const contentType = response.headers.get('content-type');
    const hasJsonContent = contentType && contentType.includes('application/json');

    let data: any = null;

    // Only try to parse JSON if the response has JSON content
    if (hasJsonContent && response.body) {
      const text = await response.text();
      if (text.trim()) {
        data = JSON.parse(text);
      }
    } else if (response.body) {
      // For non-JSON responses, get the text
      data = await response.text();
    }

    if (!response.ok) {
      const error = (data && typeof data === 'object' && data.error) ||
                   (typeof data === 'string' ? data : null) ||
                   response.statusText;
      return { data: null, error, status };
    }

    // For successful responses, return the data
    return { data, status };
  } catch (error) {
    // For successful responses that failed to parse, return success with null data
    if (response.ok) {
      return { data: null, status };
    }

    // For failed responses, return error
    return { data: null, error: 'An unexpected error occurred', status };
  }
}

/**
 * Properties service for property-related API calls
 */
export const propertiesService = {
  /**
   * Get all properties
   */
  async getProperties() {
    return api.get<Property[]>('/properties');
  },

  /**
   * Create a new property
   * @param data Property data
   */
  async createProperty(data: CreatePropertyRequest) {
    const formData = new FormData();

    // Separate images from property data
    const { images, ...propertyData } = data;

    // Add propertyDto as JSON string (required by API contract)
    formData.append('propertyDto', JSON.stringify(propertyData));

    // Add images if present (required by API contract)
    if (images && images.length > 0) {
      images.forEach(image => {
        formData.append('images', image);
      });
    }

    // Use the existing API service with custom multipart request
    return uploadPropertyWithFormData('/properties', formData);
  },

  /**
   * Get a property by ID with token
   * @param id Property ID
   * @param token Property access token (optional)
   */
  async getPropertyById(id: string, token?: string) {
    // Based on the error "Required URI template variable 'token'",
    // the backend expects: GET /properties/{id}/{token}

    // Strategy 1: Try with provided token
    if (token) {
      try {
        return await api.get<PropertyDetails>(`/properties/${id}`);
      } catch (error) {
        console.warn('Failed with provided token, trying other approaches');
      }
    }

    // Strategy 2: Try with a placeholder token (some APIs use this pattern)
    try {
      return await api.get<PropertyDetails>(`/properties/${id}`);
    } catch (error) {
      console.warn('Failed with default token, trying auth token approach');
    }

    // Strategy 3: Try with auth token (truncated for path parameter)
    try {
      const Cookies = (await import('js-cookie')).default;
      const authToken = Cookies.get('id_token') || Cookies.get('access_token');

      if (authToken) {
        // Use first 10 characters of auth token as property token
        const shortToken = authToken.substring(0, 10);
        return await api.get<PropertyDetails>(`/properties/${id}`);
      }
    } catch (error) {
      console.warn('Failed with auth token approach');
    }

    // Strategy 4: Fallback to original endpoint (will likely fail)
    console.warn('All token strategies failed, trying original endpoint');
    return api.get<PropertyDetails>(`/properties/${id}`);
  },

  /**
   * Update a property
   * @param id Property ID
   * @param data Updated property data
   */
  async updateProperty(id: string, data: Partial<CreatePropertyRequest>) {
    return api.put(`/properties/${id}`, data);
  },

  /**
   * Update property status
   * @param data Status update data
   */
  async updatePropertyStatus(data: PropertyStatusUpdateRequest) {
    return api.put('/properties/status', data);
  },

  /**
   * Delete a property
   * @param id Property ID
   */
  async deleteProperty(id: string) {
    return api.delete(`/properties/${id}`);
  },

  /**
   * Get property candidates/applications
   * @param propertyId Property ID
   */
  async getPropertyCandidates(propertyId: string) {
    return api.get(`/properties/${propertyId}/candidates`);
  }
};

// Re-export types for convenience
export type { Property, PropertyDetails, PropertyStatus, CreatePropertyRequest, PropertyStatusUpdateRequest };

export default propertiesService;
