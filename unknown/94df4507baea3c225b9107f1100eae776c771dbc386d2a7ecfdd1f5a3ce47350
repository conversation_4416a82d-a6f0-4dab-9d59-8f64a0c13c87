/**
 * User and account-related type definitions
 */

import { BusinessType, Language, BaseEntity } from './common.types';

export interface DashboardData {
  email: string;
  businessType: BusinessType;
  name: string;
  phone: string;
  preferredLanguage: string;
  emailConfirmed: boolean;
  idVerified: boolean;
  activePropertiesNumber: number;
  visitNumber: number;
  applicationNumber: number;
}

export interface ProfileData {
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string;
    address?: string;
    city?: string;
    state?: string;
    zipCode?: string;
  };
  businessHours: {
    monday: { start: string; end: string; closed: boolean };
    tuesday: { start: string; end: string; closed: boolean };
    wednesday: { start: string; end: string; closed: boolean };
    thursday: { start: string; end: string; closed: boolean };
    friday: { start: string; end: string; closed: boolean };
    saturday: { start: string; end: string; closed: boolean };
    sunday: { start: string; end: string; closed: boolean };
  };
  preferences: {
    notifications: boolean;
    twoFactorAuth: boolean;
    language: string;
  };
}

export interface BusinessHour {
  dayOfWeek: string;
  openTime: string;
  closeTime: string;
  isClosed: boolean;
}

export interface BusinessHourResponse {
  dayOfWeek: string;
  openTime: string;
  closeTime: string;
  closeAllDay: boolean;
}

export interface UpdatePersonalInfoRequest {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
}

export interface UpdateBusinessHoursRequest {
  monday: { start: string; end: string; closed: boolean };
  tuesday: { start: string; end: string; closed: boolean };
  wednesday: { start: string; end: string; closed: boolean };
  thursday: { start: string; end: string; closed: boolean };
  friday: { start: string; end: string; closed: boolean };
  saturday: { start: string; end: string; closed: boolean };
  sunday: { start: string; end: string; closed: boolean };
}

export interface UpdatePreferencesRequest {
  notifications?: boolean;
  twoFactorAuth?: boolean;
  language?: string;
}

export interface VerificationStatus {
  status: 'pending' | 'approved' | 'rejected' | 'not_started';
  message?: string;
  details?: {
    firstName?: string;
    lastName?: string;
    dateOfBirth?: string;
    documentType?: string;
    documentNumber?: string;
    expiryDate?: string;
    selfieUrl?: string;
    documentFrontUrl?: string;
    documentBackUrl?: string;
    rejectionReason?: string;
  };
}

export interface SubmitVerificationRequest {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  documentType: string;
  documentNumber: string;
  expiryDate: string;
  selfie: File | Blob;
  documentFront: File | Blob;
  documentBack?: File | Blob;
}
