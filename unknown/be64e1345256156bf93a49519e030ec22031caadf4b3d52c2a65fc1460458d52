import { toast } from "@/components/ui/use-toast";

/**
 * Standard API response format
 */
export interface ApiResponse<T = any> {
  data: T;
  error?: string;
  status: number;
}

/**
 * Error handler for API requests
 * @param error Error object
 * @param customMessage Optional custom error message
 * @returns Standardized error response
 */
export const handleApiError = (error: unknown, customMessage?: string): ApiResponse<null> => {
  const errorMessage = customMessage || 
    (error instanceof Error ? error.message : 'An unexpected error occurred');
  
  // Show toast notification for errors
  toast({
    title: "Error",
    description: errorMessage,
    variant: "destructive",
  });
  
  // Log error to console for debugging
  console.error('API Error:', error);
  
  // Return standardized error response
  return {
    data: null,
    error: errorMessage,
    status: 500,
  };
};

/**
 * Format successful API response
 * @param data Response data
 * @param status HTTP status code
 * @returns Standardized success response
 */
export const formatApiResponse = <T>(data: T, status = 200): ApiResponse<T> => {
  return {
    data,
    status,
  };
};

/**
 * Show success toast notification
 * @param message Success message
 */
export const showSuccessToast = (message: string): void => {
  toast({
    title: "Success",
    description: message,
    variant: "default",
  });
};

/**
 * Build URL with query parameters
 * @param endpoint API endpoint
 * @param params Query parameters
 * @returns Formatted URL
 */
export const buildApiUrl = (endpoint: string, params?: Record<string, string>): string => {
  const url = new URL(endpoint, window.location.origin);
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, value);
      }
    });
  }
  
  return url.toString();
};
