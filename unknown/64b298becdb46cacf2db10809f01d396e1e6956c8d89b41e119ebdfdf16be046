@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 224 71.4% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 224 71.4% 4.1%;
  --popover: 0 0% 100%;
  --popover-foreground: 224 71.4% 4.1%;
  --primary: 263 70% 50%;
  --primary-foreground: 210 40% 98%;
  --secondary: 220 14.3% 95.9%;
  --secondary-foreground: 220.9 39.3% 11%;
  --muted: 220 14.3% 95.9%;
  --muted-foreground: 220 8.9% 46.1%;
  --accent: 220 14.3% 95.9%;
  --accent-foreground: 220.9 39.3% 11%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 220 13% 91%;
  --input: 220 13% 91%;
  --ring: 263 70% 50%;
  --radius: 0.75rem;
}

.dark {
  --background: 267 31% 11%;
  --foreground: 210 40% 98%;
  --card: 267 31% 11%;
  --card-foreground: 210 40% 98%;
  --popover: 267 31% 11%;
  --popover-foreground: 210 40% 98%;
  --primary: 263 70% 50%;
  --primary-foreground: 210 40% 98%;
  --secondary: 217 19% 27%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217 19% 27%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217 19% 27%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217 19% 27%;
  --input: 217 19% 27%;
  --ring: 263 70% 50%;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground antialiased;
  }
}

.gradient-bg {
  background: linear-gradient(135deg, hsl(267 31% 11%) 0%, hsl(267 31% 11%) 50%, hsl(263 70% 50%) 100%);
}