import { api } from '../api-instance';
import type { VerificationStatus, SubmitVerificationRequest } from '@/lib/types';

/**
 * Verification service for identity verification-related API calls
 */
export const verificationService = {
  /**
   * Get verification status for the current user
   */
  async getVerificationStatus() {
    return api.get<VerificationStatus>('/accounts/verification/status');
  },

  /**
   * Submit individual verification documents
   * @param data Verification data including documents
   */
  async submitIndividualVerification(data: SubmitVerificationRequest) {
    const formData = new FormData();
    
    // Add text fields
    formData.append('firstName', data.firstName);
    formData.append('lastName', data.lastName);
    formData.append('dateOfBirth', data.dateOfBirth);
    formData.append('documentType', data.documentType);
    formData.append('documentNumber', data.documentNumber);
    formData.append('expiryDate', data.expiryDate);
    
    // Add files
    formData.append('selfie', data.selfie);
    formData.append('documentFront', data.documentFront);
    if (data.documentBack) {
      formData.append('documentBack', data.documentBack);
    }
    
    // Use the upload method with the files
    const files = [data.selfie, data.documentFront];
    if (data.documentBack) {
      files.push(data.documentBack);
    }
    
    return api.upload<VerificationStatus>('/accounts/verification/individual', files, {
      headers: {
        // Content-Type is set automatically by the upload method
      }
    });
  },

  /**
   * Cancel a pending verification request
   */
  async cancelVerification() {
    return api.post('/accounts/verification/cancel', {});
  },

  /**
   * Resubmit verification after rejection
   * @param data Updated verification data
   */
  async resubmitVerification(data: SubmitVerificationRequest) {
    return this.submitIndividualVerification(data);
  }
};

export default verificationService;
