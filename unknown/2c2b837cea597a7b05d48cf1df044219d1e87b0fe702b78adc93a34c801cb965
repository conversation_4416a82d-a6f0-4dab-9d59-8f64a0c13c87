"use client"

import { useState, useEffect, useRef } from "react"
import { Input } from "@/components/ui/input"
import { MapPin } from "lucide-react"
import env from "@/lib/env"
import { cn } from "@/lib/utils"

interface AddressSearchProps {
  onAddressSelect: (address: AddressResult) => void
  defaultValue?: string
  required?: boolean
  placeholder?: string
  className?: string
}

export interface AddressResult {
  street: string
  city: string
  province: string
  postalCode: string
  country: string
  fullAddress: string
}

export function AddressSearch({
  onAddressSelect,
  defaultValue = "",
  required = false,
  placeholder = "Search for an address in Quebec...",
  className
}: AddressSearchProps) {
  const [query, setQuery] = useState(defaultValue)
  const [suggestions, setSuggestions] = useState<any[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [loading, setLoading] = useState(false)
  const suggestionsRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current && 
        !suggestionsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Search for addresses using Mapbox Geocoding API
  const searchAddress = async (searchQuery: string) => {
    if (!searchQuery || searchQuery.length < 3) {
      setSuggestions([])
      return
    }

    setLoading(true)
    try {
      // Use Mapbox Geocoding API to search for addresses in Quebec
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
          searchQuery
        )}.json?access_token=${env.MAPBOX.TOKEN}&country=ca&types=address&bbox=-74.5,45.0,-71.0,47.0&language=en&limit=5`
      )

      if (!response.ok) {
        throw new Error("Failed to fetch address suggestions")
      }

      const data = await response.json()
      // Filter results to only include Quebec addresses
      const quebecResults = data.features.filter((feature: any) => {
        const province = feature.context?.find((ctx: any) => ctx.id.startsWith('region'))?.text
        return province === 'Quebec' || province === 'Québec'
      })
      setSuggestions(quebecResults)
      setShowSuggestions(true)
    } catch (error) {
      console.error("Error searching for address:", error)
      setSuggestions([])
    } finally {
      setLoading(false)
    }
  }

  // Parse Mapbox result into address components
  const parseAddressComponents = (feature: any): AddressResult => {
    const { context, place_name, text } = feature
    
    // Default values
    let street = text || ""
    let city = ""
    let province = "Quebec"
    let postalCode = ""
    let country = "Canada"
    
    // Extract components from context
    if (context && Array.isArray(context)) {
      context.forEach((item) => {
        if (item.id.startsWith("place")) {
          city = item.text
        } else if (item.id.startsWith("region")) {
          province = item.text
        } else if (item.id.startsWith("postcode")) {
          postalCode = item.text
        } else if (item.id.startsWith("country")) {
          country = item.text
        }
      })
    }
    
    return {
      street,
      city,
      province,
      postalCode,
      country,
      fullAddress: place_name
    }
  }

  // Handle selecting an address from suggestions
  const handleSelectAddress = (feature: any) => {
    const addressResult = parseAddressComponents(feature)
    setQuery(feature.place_name)
    setShowSuggestions(false)
    onAddressSelect(addressResult)
  }

  // Debounce search to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      searchAddress(query)
    }, 300)

    return () => clearTimeout(timer)
  }, [query])

  return (
    <div className={cn("relative w-full space-y-2", className)}>
      <div className="relative group">
        <MapPin className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400 group-focus-within:text-purple-500 transition-colors duration-200 z-10" />
        <Input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => query.length >= 3 && setShowSuggestions(true)}
          placeholder={placeholder}
          className="pl-12 pr-4 py-3 text-base bg-gradient-to-r from-slate-50 to-white border-slate-200/60 rounded-2xl focus:ring-2 focus:ring-purple-500/20 focus:border-purple-400 transition-all duration-200 shadow-sm"
          required={required}
        />
        {loading && (
          <div className="absolute right-4 top-1/2 -translate-y-1/2">
            <div className="h-5 w-5 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
      </div>

      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-[9999] mt-1 w-full bg-white shadow-2xl rounded-xl border border-gray-200/60 max-h-60 overflow-y-auto backdrop-blur-sm"
        >
          {suggestions.map((feature, index) => (
            <div
              key={feature.id}
              className={`px-4 py-3 hover:bg-gradient-to-r hover:from-purple-50 hover:to-purple-100/50 cursor-pointer transition-all duration-200 ${
                index === 0 ? 'rounded-t-xl' : ''
              } ${
                index === suggestions.length - 1 ? 'rounded-b-xl' : 'border-b border-gray-100/60'
              }`}
              onClick={() => handleSelectAddress(feature)}
            >
              <div className="flex items-start gap-3">
                <div className="p-1.5 bg-purple-100 rounded-lg mt-0.5">
                  <MapPin className="h-4 w-4 text-purple-600 flex-shrink-0" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-semibold text-gray-900 truncate">{feature.text}</div>
                  <div className="text-gray-600 text-sm mt-0.5 truncate">{feature.place_name.replace(feature.text + ', ', '')}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      <p className="text-xs text-gray-500 mt-1">Enter your complete address including street, city, and postal code</p>
    </div>
  )
}
