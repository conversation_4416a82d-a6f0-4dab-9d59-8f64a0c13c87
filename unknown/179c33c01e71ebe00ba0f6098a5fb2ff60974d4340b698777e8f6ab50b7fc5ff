/**
 * Validation Schema Tests
 */

import { signupSchema, propertySchema, loginSchema } from '@/lib/validations';

describe('Validation Schemas', () => {
  describe('signupSchema', () => {
    it('validates correct individual signup data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'Password123',
        confirmPassword: 'Password123',
        accountType: 'individual' as const,
        firstName: 'John',
        lastName: 'Doe',
        preferredLang: false,
      };

      const result = signupSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('validates correct company signup data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'Password123',
        confirmPassword: 'Password123',
        accountType: 'company' as const,
        companyName: 'Acme Inc.',
        preferredLang: false,
      };

      const result = signupSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('rejects invalid email', () => {
      const invalidData = {
        email: 'invalid-email',
        password: 'Password123',
        confirmPassword: 'Password123',
        accountType: 'individual' as const,
        firstName: 'John',
        lastName: 'Doe',
        preferredLang: false,
      };

      const result = signupSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('rejects weak password', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'weak',
        confirmPassword: 'weak',
        accountType: 'individual' as const,
        firstName: 'John',
        lastName: 'Doe',
        preferredLang: false,
      };

      const result = signupSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('rejects mismatched passwords', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'Password123',
        confirmPassword: 'DifferentPassword123',
        accountType: 'individual' as const,
        firstName: 'John',
        lastName: 'Doe',
        preferredLang: false,
      };

      const result = signupSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('loginSchema', () => {
    it('validates correct login data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const result = loginSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('rejects empty email', () => {
      const invalidData = {
        email: '',
        password: 'password123',
      };

      const result = loginSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('rejects empty password', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: '',
      };

      const result = loginSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('propertySchema', () => {
    it('validates correct property data', () => {
      const validData = {
        title: 'Beautiful Apartment',
        type: 'apartment',
        description: 'A beautiful apartment in downtown',
        address: '123 Main St',
        city: 'Montreal',
        province: 'QC',
        zip: 'H1A 1A1',
        bedrooms: '2',
        bathrooms: '1',
        price: '1500.00',
        isFreeNow: true,
        isPetFriendly: false,
        isSmokingAllowed: false,
        isFurnished: false,
        isSemiFurnished: false,
        isParkingAvailable: false,
        requireIdVerification: true,
        requireCreditScore: true,
        requireTAL: false,
        requireBackground: false,
        requireReference: false,
      };

      const result = propertySchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('rejects invalid price format', () => {
      const invalidData = {
        title: 'Beautiful Apartment',
        type: 'apartment',
        description: 'A beautiful apartment in downtown',
        address: '123 Main St',
        city: 'Montreal',
        province: 'QC',
        zip: 'H1A 1A1',
        bedrooms: '2',
        bathrooms: '1',
        price: 'invalid-price',
        isFreeNow: true,
        isPetFriendly: false,
        isSmokingAllowed: false,
        isFurnished: false,
        isSemiFurnished: false,
        isParkingAvailable: false,
        requireIdVerification: true,
        requireCreditScore: true,
        requireTAL: false,
        requireBackground: false,
        requireReference: false,
      };

      const result = propertySchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });
});
