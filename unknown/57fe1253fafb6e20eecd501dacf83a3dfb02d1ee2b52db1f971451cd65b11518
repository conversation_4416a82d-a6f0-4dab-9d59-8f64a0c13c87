"use client"

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import Cookies from 'js-cookie'
import { Loader2, Building2 } from 'lucide-react'

interface AuthGuardProps {
  children: React.ReactNode
}

// Public routes that don't require authentication
const PUBLIC_ROUTES = ['/login', '/signup', '/', '/api/auth/callback', '/logout', '/api/auth/logout']

export function AuthGuard({ children }: AuthGuardProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    const checkAuth = () => {
      // Skip auth check for public routes
      if (PUBLIC_ROUTES.some(route => pathname === route || pathname.startsWith(`${route}/`))) {
        setIsAuthenticated(true)
        setIsLoading(false)
        return
      }

      // Check for authentication tokens - check both possible cookie names
      const idToken = Cookies.get('id_token')
      const sessionToken = Cookies.get('session') // This contains the access token

      console.log(`AuthGuard: Checking authentication for ${pathname}`)
      console.log('AuthGuard: Available cookies:', {
        id_token: idToken ? 'EXISTS' : 'NOT FOUND',
        session: sessionToken ? 'EXISTS' : 'NOT FOUND',
        all_cookies: document.cookie
      })

      // if (!idToken && !sessionToken) {
      //   console.log(`AuthGuard: No authentication tokens found for ${pathname}, redirecting to login`)
      //   const loginUrl = `/login?redirect=${encodeURIComponent(pathname)}`
      //   router.push(loginUrl)
      //   return
      // }

      // Basic token validation (you can enhance this)
      try {
        // Check either id_token or session token for expiration
        const tokenToValidate = idToken || sessionToken

        if (tokenToValidate) {
          // Parse JWT to check expiration (basic check)
          const payload = JSON.parse(atob(tokenToValidate.split('.')[1]))
          const currentTime = Math.floor(Date.now() / 1000)

          if (payload.exp && payload.exp < currentTime) {
            console.log('Token expired, redirecting to login')
            // Clear expired tokens
            Cookies.remove('id_token')
            Cookies.remove('session')
            Cookies.remove('refresh_token')
            const loginUrl = `/login?redirect=${encodeURIComponent(pathname)}`
            router.push(loginUrl)
            return
          }
        }

        console.log(`Authenticated access to ${pathname}`)
        setIsAuthenticated(true)
      } catch (error) {
        console.error('Token validation error:', error)
        // Clear invalid tokens
        Cookies.remove('id_token')
        Cookies.remove('session')
        Cookies.remove('refresh_token')
        const loginUrl = `/login?redirect=${encodeURIComponent(pathname)}`
        router.push(loginUrl)
        return
      }
      
      setIsLoading(false)
    }

    checkAuth()
  }, [pathname, router])

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gray-50">
        <div className="relative">
          <div className="h-16 w-16 rounded-full bg-indigo-100 flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
          </div>
          <div className="absolute -top-2 -right-2 h-6 w-6 rounded-full bg-white flex items-center justify-center shadow-md">
            <Building2 className="h-3.5 w-3.5 text-indigo-600" />
          </div>
        </div>
        <span className="text-gray-600 font-medium mt-4">Checking authentication...</span>
      </div>
    )
  }

  // Only render children if authenticated or on public routes
  if (!isAuthenticated) {
    return null
  }

  return <>{children}</>
}
