import { jwtVerify } from "jose";
import { cookies } from "next/headers";
import { NextRequest } from "next/server";

export const COGNITO_CONFIG = {
  DOMAIN: "ca-central-1mzuuzispv.auth.ca-central-1.amazoncognito.com",
  CLIENT_ID: "58km7m63f6tnlilr3kg4kq3ht8",
  CLIENT_SECRET: "o9c6hqheg9kive9ugu2je23j1rv02qb0fjn2ql2cscv3or7n3d1",
  USER_POOL_ID: "ca-central-1_MzUUZiSpV",
  REGION: "ca-central-1",
  REDIRECT_URI: typeof window !== "undefined" ? `${window.location.origin}/dashboard` : "",
};

export async function getAuthUrl() {
  const params = new URLSearchParams({
    client_id: COGNITO_CONFIG.CLIENT_ID,
    redirect_uri: COGNITO_CONFIG.REDIRECT_URI,
    response_type: "code",
    scope: "email openid profile",
  });

  return `https://${COGNITO_CONFIG.DOMAIN}/login?${params.toString()}`;
}

export async function exchangeCodeForTokens(code: string) {
  const params = new URLSearchParams({
    grant_type: "authorization_code",
    client_id: COGNITO_CONFIG.CLIENT_ID,
    code,
    redirect_uri: COGNITO_CONFIG.REDIRECT_URI,
  });

  const basicAuth = Buffer.from(
    `${COGNITO_CONFIG.CLIENT_ID}:${COGNITO_CONFIG.CLIENT_SECRET}`
  ).toString("base64");

  const response = await fetch(
    `https://${COGNITO_CONFIG.DOMAIN}/oauth2/token`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${basicAuth}`,
      },
      body: params.toString(),
    }
  );

  if (!response.ok) {
    throw new Error("Failed to exchange code for tokens");
  }

  return response.json();
}

export async function verifyToken(token: string) {
  try {
    const { payload } = await jwtVerify(
      token,
      new TextEncoder().encode(COGNITO_CONFIG.CLIENT_SECRET)
    );
    return payload;
  } catch (error) {
    return null;
  }
}

export async function getSession() {
  const cookieStore = cookies();
  const token = cookieStore.get("session");
  
  if (!token) {
    return null;
  }

  return verifyToken(token.value);
}

export async function validateRequest(request: NextRequest) {
  const session = await getSession();
  
  if (!session) {
    return false;
  }
  
  return true;
}