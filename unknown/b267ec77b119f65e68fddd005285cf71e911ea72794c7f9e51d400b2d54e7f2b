/**
 * Authentication-related type definitions
 */

import { BusinessType, Language } from './common.types';

export interface CognitoConfig {
  DOMAIN: string;
  CLIENT_ID: string;
  CLIENT_SECRET: string;
  USER_POOL_ID: string;
  REGION: string;
  REDIRECT_URI?: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  id_token?: string;
  token_type?: string;
  expires_in?: number;
}

export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  businessName?: string;
  phone?: string;
  businessType: BusinessType;
  preferredLanguage: Language;
  emailConfirmed: boolean;
  idVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignupData {
  email: string;
  password: string;
  confirmPassword: string;
  accountType: 'individual' | 'company';
  firstName?: string;
  lastName?: string;
  companyName?: string;
  preferredLang: boolean;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}
