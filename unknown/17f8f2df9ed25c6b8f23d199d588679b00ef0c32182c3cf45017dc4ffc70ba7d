"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { Logo } from "@/components/shared/logo"
import Link from "next/link"
import { ArrowLeft, CheckCircle, Mail, Loader2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useSearchParams } from "next/navigation"
import env from "@/lib/env"
import authService from "@/lib/api/auth-service"

export default function SignupConfirmationPage() {
  const [isResending, setIsResending] = useState(false)
  const [hasResent, setHasResent] = useState(false)
  const { toast } = useToast()
  const searchParams = useSearchParams()
  const email = searchParams.get('email') || ''

  async function handleResendEmail() {
    if (!email) {
      toast({
        title: "Error",
        description: "Email address is missing. Please go back to signup.",
        variant: "destructive",
      })
      return
    }

    setIsResending(true)
    
    try {
      const result = await authService.resendConfirmationEmail(email)
      
      if (result.success) {
        setHasResent(true)
        toast({
          title: "Confirmation Email Sent",
          description: "Please check your inbox for the confirmation link.",
        })
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error("Error resending email:", error)
      toast({
        title: "Failed to Resend Email",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setIsResending(false)
    }
  }

  return (
    <main className="min-h-screen gradient-bg flex items-center justify-center p-4">
      {!env.isProd && (
        <div className={`absolute top-0 left-0 right-0 text-black text-xs text-center py-1 ${
          env.isStaging ? 'bg-orange-400' : 'bg-amber-500'
        }`}>
          {env.ENVIRONMENT.toUpperCase()} ENVIRONMENT - API: {env.API_URL}
        </div>
      )}
      
      <div className="absolute top-4 left-4 z-10">
        <Button variant="ghost" size="icon" asChild className="text-white/80 hover:text-white hover:bg-white/10 transition-all">
          <Link href="/">
            <ArrowLeft className="h-5 w-5" />
          </Link>
        </Button>
      </div>
      
      <div className="absolute top-4 right-4 z-10">
        <Logo className="scale-75" />
      </div>
      
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md mx-auto"
      >
        <Card className="bg-secondary/20 backdrop-blur-md border-white/10 shadow-2xl overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 via-green-400 to-transparent"></div>
          
          <CardHeader className="space-y-4 pb-6">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="flex flex-col items-center"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 200, damping: 10, delay: 0.3 }}
                className="mb-6"
              >
                <CheckCircle className="h-20 w-20 text-green-500" />
              </motion.div>
              
              <CardTitle className="text-3xl font-bold text-center bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                Account Created Successfully
              </CardTitle>
              <CardDescription className="text-center text-white/60 mt-2 max-w-xs mx-auto">
                We've sent a confirmation email to {email ? <strong className="text-white">{email}</strong> : "your inbox"}. Please check your email to verify your account.
              </CardDescription>
            </motion.div>
          </CardHeader>
          
          <CardContent className="pb-6">
            <motion.div 
              className="flex flex-col items-center space-y-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
            >
              <div className="bg-secondary/30 rounded-lg p-4 border border-white/10 max-w-xs w-full">
                <div className="flex items-start space-x-3">
                  <Mail className="h-6 w-6 text-primary mt-0.5" />
                  <div>
                    <h3 className="font-medium text-white">Check Your Email</h3>
                    <p className="text-sm text-white/60 mt-1">
                      The confirmation link will expire in 24 hours. If you don't see the email, check your spam folder.
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          </CardContent>
          
          <CardFooter className="flex flex-col items-center gap-4 pb-8">
            <Button asChild className="w-full max-w-xs">
              <Link href="/login">
                Continue to Login
              </Link>
            </Button>
            
            <motion.div 
              className="text-sm text-white/60"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              {!hasResent ? (
                <button 
                  onClick={handleResendEmail}
                  disabled={isResending} 
                  className="text-primary hover:text-primary/90 hover:underline transition-colors disabled:opacity-50 flex items-center gap-2"
                >
                  {isResending && <Loader2 className="h-3 w-3 animate-spin" />}
                  Didn't receive an email? Resend confirmation
                </button>
              ) : (
                <span className="text-green-500">Email resent successfully</span>
              )}
            </motion.div>
          </CardFooter>
        </Card>
      </motion.div>
    </main>
  )
}
