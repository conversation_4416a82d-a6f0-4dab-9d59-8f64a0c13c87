/**
 * Logo Component Tests
 */

import { render, screen } from '@testing-library/react';
import { Logo } from '@/components/shared/logo';

describe('Logo Component', () => {
  it('renders the logo', () => {
    render(<Logo />);
    
    // Test that the logo is rendered
    const logo = screen.getByRole('img', { name: /vestral managers/i });
    expect(logo).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const customClass = 'custom-logo-class';
    render(<Logo className={customClass} />);
    
    const logoContainer = screen.getByRole('img', { name: /vestral managers/i }).parentElement;
    expect(logoContainer).toHaveClass(customClass);
  });

  it('has correct accessibility attributes', () => {
    render(<Logo />);
    
    const logo = screen.getByRole('img', { name: /vestral managers/i });
    expect(logo).toHaveAttribute('alt');
  });
});
