{"name": "vestral-manager", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "env:dev": "node ./scripts/generate-env.js development", "env:staging": "node ./scripts/generate-env.js staging", "env:prod": "node ./scripts/generate-env.js production", "dev:local": "npm run env:dev && npm run dev", "dev:staging": "npm run env:staging && npm run dev", "dev:prod": "npm run env:prod && npm run dev"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.535.0", "@hookform/resolvers": "^3.10.0", "@mapbox/mapbox-gl-geocoder": "^5.0.3", "@next/swc-wasm-nodejs": "13.5.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@types/js-cookie": "^3.0.6", "@types/mapbox__mapbox-gl-geocoder": "^5.0.0", "@types/mapbox-gl": "^3.4.1", "@types/node": "20.6.2", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.15", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "eslint": "8.49.0", "eslint-config-next": "13.5.1", "framer-motion": "^12.4.7", "input-otp": "^1.2.4", "jose": "^5.2.3", "js-cookie": "^3.0.5", "lucide-react": "^0.446.0", "mapbox-gl": "^3.13.0", "next": "13.5.1", "next-themes": "^0.3.0", "postcss": "8.4.30", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-map-gl": "^8.0.1", "react-resizable-panels": "^2.1.3", "react-webcam": "^7.2.0", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2", "vaul": "^0.9.9", "zod": "^3.24.2"}, "devDependencies": {"@types/react-beautiful-dnd": "^13.1.8"}}