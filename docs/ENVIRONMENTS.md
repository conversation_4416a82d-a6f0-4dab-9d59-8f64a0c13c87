# Environment Configuration Guide

This guide explains how to configure and use different environments for the Vestral Managers application.

## Available Environments

The application supports three environments:

- **Development** (`development`): Local development environment
- **Staging** (`staging`): Pre-production testing environment
- **Production** (`production`): Live production environment

## Environment Configuration

### Setting up Environment Variables

1. **Using npm scripts**:

   The easiest way to configure an environment is to use the built-in npm scripts:

   ```bash
   # Development environment (default)
   npm run env:dev

   # Staging environment
   npm run env:staging

   # Production environment
   npm run env:prod
   ```

   These scripts will generate a `.env.local` file with the appropriate configuration.

2. **Running the app with a specific environment**:

   To run the app with a specific environment in one command:

   ```bash
   # Run with staging environment
   npm run dev:staging

   # Run with production environment
   npm run dev:prod
   ```

   For development, simply use the regular `npm run dev` command.

3. **Manual Configuration**:

   You can also manually create a `.env.local` file based on the `.env.example` template.

## Environment Indicators

When running in development or staging, the application displays an environment banner at the top of the page showing:

- Current environment name
- API URL being used

This banner is automatically hidden in production.

## Modifying Environment Configurations

To modify the environment configurations:

1. Open `scripts/generate-env.js`
2. Update the `configs` object with your desired values
3. Run the appropriate npm script to regenerate the `.env.local` file

## Environment Variables Reference

| Variable | Description | Example |
|----------|-------------|---------|
| NEXT_PUBLIC_ENV | Current environment | `development`, `staging`, `production` |
| NEXT_PUBLIC_API_URL | Backend API URL | `http://localhost:8081` |
| NEXT_PUBLIC_BASE_URL | Frontend base URL | `http://localhost:3000` |
| NEXT_PUBLIC_COGNITO_DOMAIN | AWS Cognito domain | `ca-central-1mzuuzispv.auth.ca-central-1.amazoncognito.com` |
| NEXT_PUBLIC_COGNITO_CLIENT_ID | AWS Cognito client ID | `58km7m63f6tnlilr3kg4kq3ht8` |
| NEXT_PUBLIC_COGNITO_CLIENT_SECRET | AWS Cognito client secret | `*******` |
| NEXT_PUBLIC_COGNITO_USER_POOL_ID | AWS Cognito user pool ID | `ca-central-1_MzUUZiSpV` |
| NEXT_PUBLIC_COGNITO_REGION | AWS Cognito region | `ca-central-1` |
