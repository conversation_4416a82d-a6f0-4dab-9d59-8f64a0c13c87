# 🏗️ Vestral Manager - Project Structure

This document outlines the enhanced project structure and organization patterns used in the Vestral Manager application.

## 📁 Directory Structure

```
vestral-manager/
├── 📁 app/                          # Next.js App Router
│   ├── 📁 (auth)/                   # Route groups for auth pages
│   │   ├── login/
│   │   ├── signup/
│   │   └── logout/
│   ├── 📁 (dashboard)/              # Protected dashboard routes
│   │   └── dashboard/
│   │       ├── analytics/
│   │       ├── candidates/
│   │       ├── properties/
│   │       ├── verification/
│   │       ├── calendar/
│   │       └── profile/
│   ├── 📁 api/                      # API routes
│   │   ├── auth/
│   │   └── ...
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
│
├── 📁 components/                   # Reusable components
│   ├── 📁 ui/                       # Base UI components (shadcn/ui)
│   ├── 📁 forms/                    # Form components
│   ├── 📁 layout/                   # Layout components
│   ├── 📁 dashboard/                # Dashboard-specific components
│   ├── 📁 auth/                     # Auth-related components
│   ├── 📁 maps/                     # Map components
│   └── 📁 shared/                   # Shared business components
│
├── 📁 lib/                          # Utilities and configurations
│   ├── 📁 api/                      # API client and services
│   ├── 📁 auth/                     # Authentication logic
│   ├── 📁 services/                 # Business logic services
│   ├── 📁 hooks/                    # Custom React hooks
│   ├── 📁 utils/                    # Utility functions
│   ├── 📁 validations/              # Zod schemas and validations
│   ├── 📁 constants/                # App constants
│   └── 📁 types/                    # TypeScript type definitions
│
├── 📁 public/                       # Static assets
├── 📁 docs/                         # Documentation
├── 📁 scripts/                      # Build and utility scripts
└── 📁 config files                  # Configuration files
```

## 🎯 Key Improvements

### 1. **Route Groups**
- `(auth)` - Authentication pages with clean URLs
- `(dashboard)` - Protected dashboard routes

### 2. **Centralized Type Definitions**
- All TypeScript types in `lib/types/`
- Organized by domain (auth, property, user, etc.)
- Single source of truth for type safety

### 3. **Validation Schemas**
- Zod schemas in `lib/validations/`
- Reusable validation logic
- Consistent error messages

### 4. **Constants Management**
- Application constants in `lib/constants/`
- Environment-specific configurations
- UI constants and theme values

### 5. **Enhanced Component Organization**
- Components grouped by functionality
- Better separation of concerns
- Easier maintenance and testing

## 🔧 Path Aliases

The following path aliases are configured for easier imports:

```typescript
"@/components/*": ["./components/*"]
"@/lib/*": ["./lib/*"]
"@/types/*": ["./lib/types/*"]
"@/hooks/*": ["./lib/hooks/*"]
"@/utils/*": ["./lib/utils/*"]
"@/services/*": ["./lib/services/*"]
"@/validations/*": ["./lib/validations/*"]
"@/constants/*": ["./lib/constants/*"]
```

## 📋 Usage Examples

### Importing Types
```typescript
import type { Property, User, ApiResponse } from "@/types";
```

### Using Validation Schemas
```typescript
import { propertySchema, signupSchema } from "@/validations";
```

### Accessing Constants
```typescript
import { API_ENDPOINTS, PROPERTY_STATUSES } from "@/constants";
```

### Using Services
```typescript
import { propertiesService, authService } from "@/services";
```

## 🚀 Benefits

1. **Scalability** - Easy to add new features and components
2. **Maintainability** - Clear separation of concerns
3. **Type Safety** - Centralized type definitions
4. **Consistency** - Standardized validation and constants
5. **Developer Experience** - Better IntelliSense and autocomplete
6. **Testing** - Easier to test individual components and services

## 📝 Best Practices

1. **Always use path aliases** for imports
2. **Define types before implementation**
3. **Use validation schemas for all forms**
4. **Keep components small and focused**
5. **Document complex business logic**
6. **Use constants instead of magic strings**

## 🔄 Migration Notes

- Updated import paths for moved components
- Consolidated duplicate type definitions
- Replaced inline validation with schema imports
- Updated component references in existing pages
