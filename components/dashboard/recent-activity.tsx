import { ScrollArea } from "@/components/ui/scroll-area"

const activities = [
  {
    user: "<PERSON>",
    action: "created a new project",
    project: "Marketing Campaign",
    time: "2 hours ago",
  },
  {
    user: "<PERSON>",
    action: "completed task",
    project: "Website Redesign",
    time: "4 hours ago",
  },
  {
    user: "<PERSON>",
    action: "added a comment",
    project: "Mobile App",
    time: "5 hours ago",
  },
  {
    user: "<PERSON>",
    action: "updated status",
    project: "Sales Report",
    time: "6 hours ago",
  },
  {
    user: "<PERSON>",
    action: "assigned task",
    project: "Product Launch",
    time: "8 hours ago",
  },
]

export function RecentActivity() {
  return (
    <ScrollArea className="h-[300px] -mx-6">
      <div className="space-y-1 px-6">
        {activities.map((activity, i) => (
          <div
            key={i}
            className="flex items-center gap-4 p-3 hover:bg-gray-50 rounded-lg transition-colors"
          >
            <div className="h-8 w-8 rounded-full bg-gradient-to-br from-primary to-primary-foreground flex items-center justify-center ring-2 ring-white">
              <span className="text-sm font-medium text-white">
                {activity.user.split(" ").map(n => n[0]).join("")}
              </span>
            </div>
            <div className="flex-1 space-y-1">
              <p className="text-sm font-medium leading-none text-gray-900">
                {activity.user}
              </p>
              <p className="text-sm text-gray-500">
                {activity.action} on <span className="text-primary font-medium">{activity.project}</span>
              </p>
            </div>
            <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
              {activity.time}
            </div>
          </div>
        ))}
      </div>
    </ScrollArea>
  )
}