"use client"

import Link from "next/link"
import { cn } from "@/lib/utils"

interface LogoProps {
  className?: string
  showText?: boolean
}

export function Logo({ className, showText = true }: LogoProps) {
  return (
    <Link href="/" className={cn("flex items-center gap-2", className)}>
      <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary">
        <span className="text-xl font-bold text-white">V</span>
      </div>
      {showText && (
        <span className="text-2xl font-bold text-gray-900">
          Vestral
        </span>
      )}
    </Link>
  )
}