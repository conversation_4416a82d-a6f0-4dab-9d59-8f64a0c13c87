"use client"

import { useState, useEffect, useRef } from "react"
import { MapPin, Building2, Bed, Bath, X, Loader2 } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { PublicProperty } from "@/lib/services/public-properties.service"

interface PropertyMapProps {
  properties: PublicProperty[]
  className?: string
}

export function PropertyMap({ properties, className = "" }: PropertyMapProps) {
  const mapContainerRef = useRef<HTMLDivElement>(null)
  const mapRef = useRef<any>(null)
  const markersRef = useRef<any[]>([])
  const [selectedProperty, setSelectedProperty] = useState<PublicProperty | null>(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const [mapError, setMapError] = useState<string | null>(null)
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null)
  const [locationPermissionDenied, setLocationPermissionDenied] = useState(false)

  // Get Mapbox token from environment
  const MAPBOX_TOKEN = process.env.NEXT_PUBLIC_MAPBOX_TOKEN

  // Filter properties with coordinates
  const propertiesWithCoords = properties.filter(property => property.coordinates)

  // Get user's current location
  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords
          setUserLocation([longitude, latitude])
        },
        (error) => {
          console.log('Geolocation error:', error.message)
          setLocationPermissionDenied(true)
          // Fallback to Montreal
          setUserLocation([-73.5673, 45.5017])
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      )
    } else {
      console.log('Geolocation is not supported by this browser')
      setLocationPermissionDenied(true)
      // Fallback to Montreal
      setUserLocation([-73.5673, 45.5017])
    }
  }, [])

  // Initialize Mapbox map
  useEffect(() => {
    if (!mapContainerRef.current || mapRef.current || !userLocation) return

    // Check if Mapbox token is available
    if (!MAPBOX_TOKEN) {
      setMapError('Mapbox token is missing')
      return
    }

    const initializeMap = async () => {
      try {
        // Dynamically import mapbox-gl
        const mapboxgl = await import('mapbox-gl')

        // Set access token
        mapboxgl.default.accessToken = MAPBOX_TOKEN

        // Determine map center priority:
        // 1. If properties exist, use their average location
        // 2. Otherwise, use user's location
        // 3. Fallback to Montreal
        let center: [number, number] = userLocation // User location or Montreal fallback
        let zoom = 12 // Default zoom for user location

        if (propertiesWithCoords.length > 0) {
          const avgLng = propertiesWithCoords.reduce((sum, property) => sum + property.coordinates!.lng, 0) / propertiesWithCoords.length
          const avgLat = propertiesWithCoords.reduce((sum, property) => sum + property.coordinates!.lat, 0) / propertiesWithCoords.length
          center = [avgLng, avgLat]
          zoom = 11 // Zoom for properties view
        }

        // Create map
        const map = new mapboxgl.default.Map({
          container: mapContainerRef.current!,
          style: 'mapbox://styles/mapbox/streets-v12',
          center: center,
          zoom: zoom
        })

        // Add navigation controls
        map.addControl(new mapboxgl.default.NavigationControl(), 'top-right')

        // Add user location marker if available and not showing properties
        if (userLocation && propertiesWithCoords.length === 0 && !locationPermissionDenied) {
          // Create user location marker
          const userMarkerEl = document.createElement('div')
          userMarkerEl.style.cssText = `
            width: 20px;
            height: 20px;
            background-color: #3B82F6;
            border: 3px solid white;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          `

          new mapboxgl.default.Marker(userMarkerEl)
            .setLngLat(userLocation)
            .addTo(map)
        }

        // Wait for map to load
        map.on('load', () => {
          setMapLoaded(true)
          addMarkersToMap(map, mapboxgl.default)
        })

        mapRef.current = map

      } catch (error) {
        console.error('Error initializing Mapbox:', error)
        setMapError('Failed to load map')
      }
    }

    initializeMap()

    // Cleanup
    return () => {
      if (mapRef.current) {
        mapRef.current.remove()
        mapRef.current = null
      }
    }
  }, [userLocation]) // Add userLocation as dependency

  // Add markers to map
  const addMarkersToMap = (map: any, mapboxgl: any) => {
    // Clear existing markers
    markersRef.current.forEach(marker => marker.remove())
    markersRef.current = []

    // Add new markers
    propertiesWithCoords.forEach((property) => {
      // Create marker element
      const el = document.createElement('div')
      el.className = 'property-marker'
      el.style.cssText = `
        width: 40px;
        height: 40px;
        background-color: #8B5CF6;
        border: 3px solid white;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        transition: all 0.2s ease;
      `
      
      // Add building icon
      el.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
          <path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"/>
          <path d="M6 12h4"/>
          <path d="M6 16h4"/>
          <path d="M16 12h2"/>
          <path d="M16 16h2"/>
        </svg>
      `

      // Add hover effects
      el.addEventListener('mouseenter', () => {
        el.style.transform = 'scale(1.1)'
        el.style.backgroundColor = '#7C3AED'
      })
      
      el.addEventListener('mouseleave', () => {
        el.style.transform = 'scale(1)'
        el.style.backgroundColor = '#8B5CF6'
      })

      // Add click handler
      el.addEventListener('click', () => {
        setSelectedProperty(property)
      })

      // Create and add marker
      const mapboxMarker = new mapboxgl.Marker(el)
        .setLngLat([property.coordinates!.lng, property.coordinates!.lat])
        .addTo(map)

      markersRef.current.push(mapboxMarker)
    })
  }

  // Update markers when properties change
  useEffect(() => {
    if (mapRef.current && mapLoaded) {
      const mapboxgl = require('mapbox-gl')
      addMarkersToMap(mapRef.current, mapboxgl)
    }
  }, [properties, mapLoaded])

  return (
    <div className={`relative ${className}`}>
      {/* Map Container */}
      <div
        ref={mapContainerRef}
        className="relative w-full h-[600px] bg-gray-100 rounded-lg overflow-hidden border border-gray-200"
      />

      {/* Loading State */}
      {!mapLoaded && !mapError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
          <div className="text-center">
            <div className="relative mb-4">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
              </div>
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-lg">
                <MapPin className="h-3.5 w-3.5 text-purple-600" />
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Map</h3>
            <p className="text-gray-600">Preparing property locations...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {mapError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
          <div className="text-center max-w-md mx-auto p-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <MapPin className="h-8 w-8 text-red-500" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Map Unavailable</h3>
            <p className="text-gray-600 mb-4">{mapError}</p>
            <Button 
              onClick={() => window.location.reload()} 
              variant="outline"
              className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
            >
              Retry
            </Button>
          </div>
        </div>
      )}

      {/* Properties Count Overlay */}
      {mapLoaded && (
        <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg border border-gray-200">
          <div className="text-sm font-semibold text-gray-900">{properties.length} Properties</div>
          <div className="text-xs text-gray-600">Available for rent</div>
        </div>
      )}

      {/* Property Details Popup */}
      {selectedProperty && (
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-10 p-4">
          <Card className="w-full max-w-md mx-auto shadow-2xl rounded-2xl overflow-hidden">
            <CardContent className="p-0">
              {/* Property Image */}
              <div className="relative h-48">
                <img
                  src={selectedProperty.images[0]}
                  alt={selectedProperty.title}
                  className="w-full h-full object-cover"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2 bg-white/90 hover:bg-white backdrop-blur-sm rounded-full"
                  onClick={() => setSelectedProperty(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
                <Badge className="absolute top-2 left-2 bg-green-500 hover:bg-green-600 text-white rounded-full">
                  Available Now
                </Badge>
              </div>

              {/* Property Details */}
              <div className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="font-bold text-xl text-gray-900 line-clamp-2 leading-tight">
                    {selectedProperty.title}
                  </h3>
                  <div className="text-right flex-shrink-0 ml-3">
                    <div className="text-2xl font-bold text-purple-600">
                      ${selectedProperty.price?.toLocaleString() || '0'}
                    </div>
                    <div className="text-sm text-gray-500">per month</div>
                  </div>
                </div>

                <div className="flex items-center gap-2 text-gray-600 mb-4">
                  <MapPin className="h-4 w-4 flex-shrink-0" />
                  <span className="text-sm line-clamp-1">{selectedProperty.address}</span>
                </div>

                <div className="flex items-center gap-6 mb-4">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                      <Bed className="h-4 w-4 text-purple-600" />
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{selectedProperty.bedrooms}</div>
                      <div className="text-xs text-gray-500">Bedrooms</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Bath className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{selectedProperty.bathrooms}</div>
                      <div className="text-xs text-gray-500">Bathrooms</div>
                    </div>
                  </div>
                </div>

                <p className="text-gray-600 text-sm line-clamp-2 leading-relaxed mb-6">
                  {selectedProperty.description}
                </p>

                <Link href={`/properties/${selectedProperty.id}`} className="block">
                  <Button className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-xl py-3">
                    View Property Details
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
