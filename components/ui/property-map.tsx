"use client"

import { useState, useEffect, useRef } from "react"
import { MapPin, Building2, Bed, Bath, Car, X } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { PublicProperty } from "@/lib/services/public-properties.service"

interface PropertyMapProps {
  properties: PublicProperty[]
  className?: string
}

interface MapMarker {
  id: string
  lat: number
  lng: number
  property: PublicProperty
}

export function PropertyMap({ properties, className = "" }: PropertyMapProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const [selectedProperty, setSelectedProperty] = useState<PublicProperty | null>(null)
  const [mapCenter, setMapCenter] = useState({ lat: 45.5017, lng: -73.5673 }) // Montreal center
  const [zoom, setZoom] = useState(11)

  // Create markers from properties with coordinates
  const markers: MapMarker[] = properties
    .filter(property => property.coordinates)
    .map(property => ({
      id: property.id,
      lat: property.coordinates!.lat,
      lng: property.coordinates!.lng,
      property
    }))

  // Simple map implementation using CSS and positioning
  // In a real app, you would use Google Maps, Mapbox, or Leaflet
  const getMarkerPosition = (marker: MapMarker) => {
    // Simple projection calculation for demo purposes
    const mapWidth = 800
    const mapHeight = 600
    
    // Quebec bounds (approximate)
    const bounds = {
      north: 47.5,
      south: 45.0,
      east: -70.0,
      west: -75.0
    }
    
    const x = ((marker.lng - bounds.west) / (bounds.east - bounds.west)) * mapWidth
    const y = ((bounds.north - marker.lat) / (bounds.north - bounds.south)) * mapHeight
    
    return { x: Math.max(0, Math.min(mapWidth, x)), y: Math.max(0, Math.min(mapHeight, y)) }
  }

  return (
    <div className={`relative ${className}`}>
      {/* Map Container */}
      <div 
        ref={mapRef}
        className="relative w-full h-[600px] bg-gradient-to-br from-blue-100 via-green-50 to-blue-50 rounded-lg overflow-hidden border border-gray-200"
        style={{
          backgroundImage: `
            radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 70%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 50% 50%, rgba(168, 85, 247, 0.05) 0%, transparent 50%)
          `
        }}
      >
        {/* Map Grid Lines */}
        <div className="absolute inset-0 opacity-20">
          {/* Vertical lines */}
          {Array.from({ length: 10 }).map((_, i) => (
            <div
              key={`v-${i}`}
              className="absolute top-0 bottom-0 w-px bg-gray-300"
              style={{ left: `${(i + 1) * 10}%` }}
            />
          ))}
          {/* Horizontal lines */}
          {Array.from({ length: 8 }).map((_, i) => (
            <div
              key={`h-${i}`}
              className="absolute left-0 right-0 h-px bg-gray-300"
              style={{ top: `${(i + 1) * 12.5}%` }}
            />
          ))}
        </div>

        {/* Location Labels */}
        <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-sm">
          <div className="text-sm font-semibold text-gray-900">Montreal Area</div>
          <div className="text-xs text-gray-600">Quebec, Canada</div>
        </div>

        <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-sm">
          <div className="text-sm font-semibold text-gray-900">{properties.length} Properties</div>
          <div className="text-xs text-gray-600">Available for rent</div>
        </div>

        {/* Property Markers */}
        {markers.map((marker) => {
          const position = getMarkerPosition(marker)
          return (
            <div
              key={marker.id}
              className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group"
              style={{
                left: `${(position.x / 800) * 100}%`,
                top: `${(position.y / 600) * 100}%`
              }}
              onClick={() => setSelectedProperty(marker.property)}
            >
              {/* Marker */}
              <div className="relative">
                <div className="w-8 h-8 bg-purple-600 rounded-full border-2 border-white shadow-lg flex items-center justify-center group-hover:bg-purple-700 transition-colors duration-200 group-hover:scale-110 transform">
                  <Building2 className="w-4 h-4 text-white" />
                </div>
                
                {/* Price Badge */}
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-white rounded-full px-2 py-1 shadow-lg border opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                  <div className="text-xs font-bold text-purple-600">
                    ${marker.property.price}/mo
                  </div>
                </div>

                {/* Pulse Animation */}
                <div className="absolute inset-0 w-8 h-8 bg-purple-600 rounded-full animate-ping opacity-20"></div>
              </div>
            </div>
          )
        })}

        {/* Map Controls */}
        <div className="absolute bottom-4 right-4 flex flex-col gap-2">
          <Button
            variant="outline"
            size="sm"
            className="bg-white/90 backdrop-blur-sm hover:bg-white"
            onClick={() => setZoom(Math.min(zoom + 1, 15))}
          >
            +
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="bg-white/90 backdrop-blur-sm hover:bg-white"
            onClick={() => setZoom(Math.max(zoom - 1, 8))}
          >
            -
          </Button>
        </div>

        {/* Legend */}
        <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-sm">
          <div className="text-xs font-semibold text-gray-900 mb-2">Legend</div>
          <div className="flex items-center gap-2 text-xs text-gray-600">
            <div className="w-3 h-3 bg-purple-600 rounded-full"></div>
            <span>Available Property</span>
          </div>
        </div>
      </div>

      {/* Property Details Popup */}
      {selectedProperty && (
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-10 p-4">
          <Card className="w-full max-w-md mx-auto shadow-2xl">
            <CardContent className="p-0">
              {/* Property Image */}
              <div className="relative">
                <img
                  src={selectedProperty.images[0]}
                  alt={selectedProperty.title}
                  className="w-full h-48 object-cover rounded-t-lg"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2 bg-white/80 hover:bg-white"
                  onClick={() => setSelectedProperty(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
                <Badge className="absolute top-2 left-2 bg-green-500">
                  Available
                </Badge>
              </div>

              {/* Property Details */}
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-lg text-gray-900 line-clamp-1">
                    {selectedProperty.title}
                  </h3>
                  <span className="text-xl font-bold text-purple-600">
                    ${selectedProperty.price}/mo
                  </span>
                </div>

                <div className="flex items-center text-gray-500 mb-3">
                  <MapPin className="h-4 w-4 mr-1" />
                  <span className="text-sm line-clamp-1">{selectedProperty.address}</span>
                </div>

                <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                  <div className="flex items-center gap-1">
                    <Bed className="h-4 w-4" />
                    <span>{selectedProperty.bedrooms} bed</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Bath className="h-4 w-4" />
                    <span>{selectedProperty.bathrooms} bath</span>
                  </div>
                  {selectedProperty.parking > 0 && (
                    <div className="flex items-center gap-1">
                      <Car className="h-4 w-4" />
                      <span>{selectedProperty.parking} parking</span>
                    </div>
                  )}
                </div>

                <p className="text-sm text-gray-600 line-clamp-2 mb-4">
                  {selectedProperty.description}
                </p>

                <div className="flex gap-2">
                  <Link href={`/properties/${selectedProperty.id}`} className="flex-1">
                    <Button className="w-full bg-purple-600 hover:bg-purple-700">
                      View Details
                    </Button>
                  </Link>
                  <Button
                    variant="outline"
                    onClick={() => setSelectedProperty(null)}
                  >
                    Close
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
