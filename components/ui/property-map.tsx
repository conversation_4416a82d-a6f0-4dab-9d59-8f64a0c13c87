"use client"

import { useState, useEffect, useRef } from "react"
import { MapPin, Building2, Bed, Bath, X, Loader2, Navigation } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { PublicProperty } from "@/lib/services/public-properties.service"

// Import Mapbox CSS
import 'mapbox-gl/dist/mapbox-gl.css'

interface PropertyMapProps {
  properties: PublicProperty[]
  className?: string
}

export function PropertyMap({ properties, className = "" }: PropertyMapProps) {
  const mapContainerRef = useRef<HTMLDivElement>(null)
  const mapRef = useRef<any>(null)
  const markersRef = useRef<any[]>([])
  const [selectedProperty, setSelectedProperty] = useState<PublicProperty | null>(null)
  const [selectedCluster, setSelectedCluster] = useState<PublicProperty[] | null>(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const [mapError, setMapError] = useState<string | null>(null)
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null)
  const [locationPermissionDenied, setLocationPermissionDenied] = useState(false)

  // Get Mapbox token from environment
  const MAPBOX_TOKEN = process.env.NEXT_PUBLIC_MAPBOX_TOKEN

  // Filter properties with coordinates
  const propertiesWithCoords = properties.filter(property => property.coordinates)

  // Function to calculate distance between two coordinates
  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number) => {
    const R = 6371 // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLng = (lng2 - lng1) * Math.PI / 180
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c * 1000 // Distance in meters
  }

  // Function to group nearby properties
  const groupProperties = (properties: PublicProperty[], maxDistance: number = 100) => {
    const groups: Array<{
      lat: number
      lng: number
      properties: PublicProperty[]
      isCluster: boolean
    }> = []

    const processed = new Set<string>()

    properties.forEach(property => {
      if (processed.has(property.id) || !property.coordinates) return

      const group = {
        lat: property.coordinates.lat,
        lng: property.coordinates.lng,
        properties: [property],
        isCluster: false
      }

      // Find nearby properties
      properties.forEach(otherProperty => {
        if (otherProperty.id === property.id ||
            processed.has(otherProperty.id) ||
            !otherProperty.coordinates) return

        const distance = calculateDistance(
          property.coordinates!.lat,
          property.coordinates!.lng,
          otherProperty.coordinates.lat,
          otherProperty.coordinates.lng
        )

        if (distance <= maxDistance) {
          group.properties.push(otherProperty)
          processed.add(otherProperty.id)
        }
      })

      processed.add(property.id)

      // Mark as cluster if more than one property
      if (group.properties.length > 1) {
        group.isCluster = true
        // Calculate center point of cluster
        const avgLat = group.properties.reduce((sum, p) => sum + p.coordinates!.lat, 0) / group.properties.length
        const avgLng = group.properties.reduce((sum, p) => sum + p.coordinates!.lng, 0) / group.properties.length
        group.lat = avgLat
        group.lng = avgLng
      }

      groups.push(group)
    })

    return groups
  }

  // Function to retry getting user location
  const retryLocation = () => {
    setUserLocation(null)
    setLocationPermissionDenied(false)

    console.log('Retrying location detection...')

    if (!navigator.geolocation) {
      console.log('Geolocation not supported')
      setLocationPermissionDenied(true)
      setUserLocation([-73.5673, 45.5017])
      return
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude, accuracy } = position.coords
        console.log('Retry location success:', { latitude, longitude, accuracy })
        setUserLocation([longitude, latitude])
        setLocationPermissionDenied(false)
      },
      (error) => {
        console.error('Retry location failed:', error)
        setLocationPermissionDenied(true)
        setUserLocation([-73.5673, 45.5017])
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0 // Force fresh location
      }
    )
  }

  // Debug logging
  useEffect(() => {
    console.log('Mapbox Token:', MAPBOX_TOKEN ? 'Present' : 'Missing')
    console.log('User Location:', userLocation)
    console.log('Properties with coords:', propertiesWithCoords.length)
  }, [userLocation, propertiesWithCoords])

  // Debug selected property changes
  useEffect(() => {
    if (selectedProperty) {
      console.log('Property selected:', selectedProperty.id, selectedProperty.title)
    } else {
      console.log('Property deselected')
    }
  }, [selectedProperty])

  // Get user's current location
  useEffect(() => {
    const getUserLocation = () => {
      console.log('Attempting to get user location...')

      if (!navigator.geolocation) {
        console.log('Geolocation is not supported by this browser')
        setLocationPermissionDenied(true)
        setUserLocation([-73.5673, 45.5017]) // Montreal fallback
        return
      }

      // Check if we're on HTTPS (required for geolocation)
      if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
        console.log('Geolocation requires HTTPS')
        setLocationPermissionDenied(true)
        setUserLocation([-73.5673, 45.5017]) // Montreal fallback
        return
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude, accuracy } = position.coords
          console.log('Location found:', { latitude, longitude, accuracy })
          setUserLocation([longitude, latitude])
          setLocationPermissionDenied(false)
        },
        (error) => {
          console.error('Geolocation error:', error)
          let errorMessage = 'Unknown error'

          switch(error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'User denied the request for Geolocation'
              break
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Location information is unavailable'
              break
            case error.TIMEOUT:
              errorMessage = 'The request to get user location timed out'
              break
          }

          console.log('Geolocation error details:', errorMessage)
          setLocationPermissionDenied(true)
          setUserLocation([-73.5673, 45.5017]) // Montreal fallback
        },
        {
          enableHighAccuracy: false, // Start with less accurate but faster
          timeout: 15000, // Increased timeout
          maximumAge: 600000 // 10 minutes cache
        }
      )

      // Try high accuracy as backup if first attempt fails
      setTimeout(() => {
        if (!userLocation) {
          console.log('Trying high accuracy location...')
          navigator.geolocation.getCurrentPosition(
            (position) => {
              const { latitude, longitude, accuracy } = position.coords
              console.log('High accuracy location found:', { latitude, longitude, accuracy })
              setUserLocation([longitude, latitude])
              setLocationPermissionDenied(false)
            },
            (error) => {
              console.log('High accuracy also failed:', error.message)
            },
            {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 300000
            }
          )
        }
      }, 5000)
    }

    getUserLocation()
  }, [])

  // Initialize Mapbox map
  useEffect(() => {
    if (!mapContainerRef.current || mapRef.current || !userLocation) return

    // Check if Mapbox token is available
    if (!MAPBOX_TOKEN) {
      setMapError('Mapbox token is missing')
      return
    }

    const initializeMap = async () => {
      try {
        console.log('Initializing map with token:', MAPBOX_TOKEN?.substring(0, 10) + '...')
        console.log('Map center will be:', userLocation)

        // Dynamically import mapbox-gl
        const mapboxgl = await import('mapbox-gl')

        // Set access token
        mapboxgl.default.accessToken = MAPBOX_TOKEN

        // Determine map center and zoom priority:
        // 1. If properties exist, use their average location with moderate zoom
        // 2. Otherwise, use user's location with city-level zoom
        // 3. Fallback to Montreal with city-level zoom
        let center: [number, number] = userLocation // User location or Montreal fallback
        let zoom = 13 // City-level zoom for user location (like your screenshot)

        if (propertiesWithCoords.length > 0) {
          const avgLng = propertiesWithCoords.reduce((sum, property) => sum + property.coordinates!.lng, 0) / propertiesWithCoords.length
          const avgLat = propertiesWithCoords.reduce((sum, property) => sum + property.coordinates!.lat, 0) / propertiesWithCoords.length
          center = [avgLng, avgLat]
          zoom = 12 // Slightly closer zoom for properties view
          console.log('Using properties center:', center)
        } else {
          // When no properties or using user location, zoom to city level
          if (!locationPermissionDenied) {
            zoom = 13 // Good city-level zoom for user's area
            console.log('Using user location with city zoom:', center, 'zoom:', zoom)
          } else {
            zoom = 11 // Wider view for Montreal fallback
            console.log('Using Montreal fallback with wider zoom:', center, 'zoom:', zoom)
          }
        }

        // Create map with error handling
        const map = new mapboxgl.default.Map({
          container: mapContainerRef.current!,
          style: 'mapbox://styles/mapbox/streets-v12',
          center: center,
          zoom: zoom,
          attributionControl: false
        })

        // Add error handling for map
        map.on('error', (e) => {
          console.error('Mapbox error:', e)
          setMapError(`Map error: ${e.error?.message || 'Unknown error'}`)
        })

        // Add style load event
        map.on('style.load', () => {
          console.log('Map style loaded successfully')
        })

        // Add source load event
        map.on('sourcedata', (e) => {
          if (e.isSourceLoaded) {
            console.log('Map source loaded:', e.sourceId)
          }
        })

        // Add navigation controls
        map.addControl(new mapboxgl.default.NavigationControl(), 'top-right')

        // Add user location marker if available and not showing properties
        if (userLocation && propertiesWithCoords.length === 0 && !locationPermissionDenied) {
          // Create user location marker
          const userMarkerEl = document.createElement('div')
          userMarkerEl.style.cssText = `
            width: 20px;
            height: 20px;
            background-color: #3B82F6;
            border: 3px solid white;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          `

          new mapboxgl.default.Marker(userMarkerEl)
            .setLngLat(userLocation)
            .addTo(map)
        }

        // Wait for map to load
        map.on('load', () => {
          setMapLoaded(true)
          addMarkersToMap(map, mapboxgl.default)

          // If using user location and no properties, ensure good zoom level
          if (propertiesWithCoords.length === 0 && !locationPermissionDenied) {
            console.log('Setting optimal zoom for user location')
            map.setZoom(13) // Ensure city-level zoom
          }
        })

        mapRef.current = map

      } catch (error) {
        console.error('Error initializing Mapbox:', error)
        setMapError('Failed to load map')
      }
    }

    initializeMap()

    // Cleanup
    return () => {
      if (mapRef.current) {
        mapRef.current.remove()
        mapRef.current = null
      }
    }
  }, [userLocation]) // Add userLocation as dependency

  // Add markers to map
  const addMarkersToMap = (map: any, mapboxgl: any) => {
    // Clear existing markers
    markersRef.current.forEach(marker => marker.remove())
    markersRef.current = []

    // Group properties by proximity
    const propertyGroups = groupProperties(propertiesWithCoords, 100) // 100 meters

    // Add markers for each group
    propertyGroups.forEach((group) => {
      // Create marker element
      const el = document.createElement('div')
      el.className = 'property-marker'

      if (group.isCluster) {
        // Cluster marker styling
        el.style.cssText = `
          width: 50px;
          height: 50px;
          background-color: #DC2626;
          border: 3px solid white;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 12px rgba(0,0,0,0.3);
          transition: all 0.2s ease;
          transform: translate(-50%, -50%);
          transform-origin: center center;
          position: relative;
          z-index: 999;
          font-weight: bold;
          color: white;
          font-size: 14px;
        `

        // Show number of properties in cluster
        el.innerHTML = `${group.properties.length}`

        // Cluster hover effects
        el.addEventListener('mouseenter', () => {
          el.style.transform = 'translate(-50%, -50%) scale(1.1)'
          el.style.backgroundColor = '#B91C1C'
          el.style.zIndex = '1000'
        })

        el.addEventListener('mouseleave', () => {
          el.style.transform = 'translate(-50%, -50%) scale(1)'
          el.style.backgroundColor = '#DC2626'
          el.style.zIndex = '999'
        })

        // Cluster click handler - show cluster popup
        el.addEventListener('click', (e) => {
          e.stopPropagation()
          console.log('Cluster clicked with', group.properties.length, 'properties')
          setSelectedCluster(group.properties)
          setSelectedProperty(null) // Clear single property selection
        })

      } else {
        // Single property marker styling
        el.style.cssText = `
          width: 40px;
          height: 40px;
          background-color: #8B5CF6;
          border: 3px solid white;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 8px rgba(0,0,0,0.2);
          transition: all 0.2s ease;
          transform: translate(-50%, -50%);
          transform-origin: center center;
          position: relative;
          z-index: 999;
        `

        // Add building icon for single property
        el.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
            <path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"/>
            <path d="M6 12h4"/>
            <path d="M6 16h4"/>
            <path d="M16 12h2"/>
            <path d="M16 16h2"/>
          </svg>
        `

        // Single property hover effects
        el.addEventListener('mouseenter', () => {
          el.style.transform = 'translate(-50%, -50%) scale(1.1)'
          el.style.backgroundColor = '#7C3AED'
          el.style.zIndex = '1000'
        })

        el.addEventListener('mouseleave', () => {
          el.style.transform = 'translate(-50%, -50%) scale(1)'
          el.style.backgroundColor = '#8B5CF6'
          el.style.zIndex = '999'
        })

        // Single property click handler
        el.addEventListener('click', (e) => {
          e.stopPropagation()
          const property = group.properties[0]
          console.log('Marker clicked for property:', property.id, property.title)
          setSelectedProperty(property)
        })
      }

      // Create and add marker
      const mapboxMarker = new mapboxgl.Marker(el)
        .setLngLat([group.lng, group.lat])
        .addTo(map)

      markersRef.current.push(mapboxMarker)
    })
  }

  // Update markers when properties change
  useEffect(() => {
    if (mapRef.current && mapLoaded) {
      const mapboxgl = require('mapbox-gl')
      addMarkersToMap(mapRef.current, mapboxgl)

      // Adjust zoom based on content
      if (propertiesWithCoords.length > 0) {
        // If we have properties, fit them in view with some padding
        const bounds = new mapboxgl.LngLatBounds()
        propertiesWithCoords.forEach(property => {
          bounds.extend([property.coordinates!.lng, property.coordinates!.lat])
        })

        // Fit bounds with padding, but don't zoom too close
        mapRef.current.fitBounds(bounds, {
          padding: 50,
          maxZoom: 14 // Don't zoom closer than this
        })
      } else if (!locationPermissionDenied && userLocation) {
        // If no properties but we have user location, maintain city zoom
        mapRef.current.setCenter(userLocation)
        mapRef.current.setZoom(13)
      }
    }
  }, [properties, mapLoaded, userLocation, locationPermissionDenied])

  return (
    <div className={`relative ${className}`}>
      {/* Map Container */}
      <div
        ref={mapContainerRef}
        className="relative w-full h-[600px] bg-gray-100 rounded-lg overflow-hidden border border-gray-200"
        style={{
          minHeight: '600px',
          position: 'relative'
        }}
      />

      {/* Loading State */}
      {!mapLoaded && !mapError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
          <div className="text-center">
            <div className="relative mb-4">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
              </div>
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-lg">
                <MapPin className="h-3.5 w-3.5 text-purple-600" />
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Map</h3>
            <p className="text-gray-600">Preparing property locations...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {mapError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
          <div className="text-center max-w-md mx-auto p-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <MapPin className="h-8 w-8 text-red-500" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Map Unavailable</h3>
            <p className="text-gray-600 mb-4">{mapError}</p>
            <Button 
              onClick={() => window.location.reload()} 
              variant="outline"
              className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
            >
              Retry
            </Button>
          </div>
        </div>
      )}

      {/* Properties Count Overlay */}
      {mapLoaded && (
        <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg border border-gray-200">
          <div className="text-sm font-semibold text-gray-900">{properties.length} Properties</div>
          <div className="text-xs text-gray-600">Available for rent</div>
        </div>
      )}

      {/* Location Indicator */}
      {mapLoaded && (
        <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg border border-gray-200">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${
                !userLocation ? 'bg-yellow-400 animate-pulse' :
                locationPermissionDenied ? 'bg-gray-400' : 'bg-green-500'
              }`}></div>
              <div>
                <div className="text-sm font-semibold text-gray-900">
                  {!userLocation ? 'Getting Location...' :
                   locationPermissionDenied ? 'Default Location' : 'Your Location'}
                </div>
                <div className="text-xs text-gray-600">
                  {!userLocation ? 'Please wait' :
                   locationPermissionDenied ? 'Montreal, QC' : 'GPS position'}
                </div>
              </div>
            </div>

            {/* Retry Location Button */}
            {locationPermissionDenied && (
              <Button
                variant="ghost"
                size="sm"
                onClick={retryLocation}
                className="h-8 w-8 p-0 hover:bg-blue-50"
                title="Try to get your location again"
              >
                <Navigation className="h-4 w-4 text-blue-600" />
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Property Details Popup */}
      {selectedProperty && (
        <div
          className="absolute inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-10 p-4"
          onClick={(e) => {
            // Close popup when clicking outside
            if (e.target === e.currentTarget) {
              setSelectedProperty(null)
            }
          }}
        >
          <Card className="w-full max-w-md mx-auto shadow-2xl rounded-2xl overflow-hidden">
            <CardContent className="p-0">
              {/* Property Image */}
              <div className="relative h-48">
                <img
                  src={selectedProperty.images[0]}
                  alt={selectedProperty.title}
                  className="w-full h-full object-cover"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2 bg-white/90 hover:bg-white backdrop-blur-sm rounded-full"
                  onClick={() => setSelectedProperty(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
                <Badge className="absolute top-2 left-2 bg-green-500 hover:bg-green-600 text-white rounded-full">
                  Available Now
                </Badge>
              </div>

              {/* Property Details */}
              <div className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="font-bold text-xl text-gray-900 line-clamp-2 leading-tight">
                    {selectedProperty.title}
                  </h3>
                  <div className="text-right flex-shrink-0 ml-3">
                    <div className="text-2xl font-bold text-purple-600">
                      ${selectedProperty.price?.toLocaleString() || '0'}
                    </div>
                    <div className="text-sm text-gray-500">per month</div>
                  </div>
                </div>

                <div className="flex items-center gap-2 text-gray-600 mb-4">
                  <MapPin className="h-4 w-4 flex-shrink-0" />
                  <span className="text-sm line-clamp-1">{selectedProperty.address}</span>
                </div>

                <div className="flex items-center gap-6 mb-4">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                      <Bed className="h-4 w-4 text-purple-600" />
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{selectedProperty.bedrooms}</div>
                      <div className="text-xs text-gray-500">Bedrooms</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Bath className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{selectedProperty.bathrooms}</div>
                      <div className="text-xs text-gray-500">Bathrooms</div>
                    </div>
                  </div>
                </div>

                <p className="text-gray-600 text-sm line-clamp-2 leading-relaxed mb-6">
                  {selectedProperty.description}
                </p>

                <Link href={`/properties/${selectedProperty.id}`} className="block">
                  <Button
                    className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-xl py-3"
                    onClick={() => {
                      console.log('Navigating to property:', selectedProperty.id)
                    }}
                  >
                    View Property Details
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Cluster Properties Popup */}
      {selectedCluster && (
        <div
          className="absolute inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-10 p-4"
          onClick={(e) => {
            // Close popup when clicking outside
            if (e.target === e.currentTarget) {
              setSelectedCluster(null)
            }
          }}
        >
          <Card className="w-full max-w-2xl mx-auto shadow-2xl rounded-2xl overflow-hidden max-h-[80vh] overflow-y-auto">
            <CardContent className="p-0">
              {/* Cluster Header */}
              <div className="relative bg-gradient-to-r from-red-500 to-red-600 p-6 text-white">
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full text-white"
                  onClick={() => setSelectedCluster(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
                <h3 className="text-2xl font-bold mb-2">
                  {selectedCluster.length} Properties in this Area
                </h3>
                <p className="text-red-100">Click on any property to view details</p>
              </div>

              {/* Properties List */}
              <div className="p-6 space-y-4">
                {selectedCluster.map((property) => (
                  <div
                    key={property.id}
                    className="flex items-center gap-4 p-4 border border-gray-200 rounded-xl hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => {
                      setSelectedProperty(property)
                      setSelectedCluster(null)
                    }}
                  >
                    {/* Property Image */}
                    <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                      <img
                        src={property.images[0]}
                        alt={property.title}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    {/* Property Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-3">
                        <h4 className="font-semibold text-gray-900 line-clamp-1">
                          {property.title}
                        </h4>
                        <div className="text-right flex-shrink-0">
                          <div className="text-lg font-bold text-purple-600">
                            ${property.price?.toLocaleString() || '0'}
                          </div>
                          <div className="text-xs text-gray-500">per month</div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2 text-gray-600 mt-1">
                        <MapPin className="h-3 w-3 flex-shrink-0" />
                        <span className="text-sm line-clamp-1">{property.address}</span>
                      </div>

                      <div className="flex items-center gap-4 mt-2">
                        <div className="flex items-center gap-1 text-sm text-gray-600">
                          <Bed className="h-3 w-3" />
                          <span>{property.bedrooms}</span>
                        </div>
                        <div className="flex items-center gap-1 text-sm text-gray-600">
                          <Bath className="h-3 w-3" />
                          <span>{property.bathrooms}</span>
                        </div>
                      </div>
                    </div>

                    {/* View Button */}
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-shrink-0"
                      onClick={(e) => {
                        e.stopPropagation()
                        window.open(`/properties/${property.id}`, '_blank')
                      }}
                    >
                      View
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
