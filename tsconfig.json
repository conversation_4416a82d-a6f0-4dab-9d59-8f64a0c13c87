{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/lib/*": ["./lib/*"], "@/app/*": ["./app/*"], "@/types/*": ["./lib/types/*"], "@/types": ["./lib/types"], "@/hooks/*": ["./lib/hooks/*"], "@/utils/*": ["./lib/utils/*"], "@/services/*": ["./lib/services/*"], "@/validations/*": ["./lib/validations/*"], "@/validations": ["./lib/validations"], "@/constants/*": ["./lib/constants/*"], "@/constants": ["./lib/constants"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}